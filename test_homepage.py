#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试首页修改
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication
from ui.main_window import MainWindow

def test_homepage():
    """测试首页功能"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = MainWindow()
    
    # 检查功能卡片数量
    home_page = window.stacked_widget.widget(0)  # 首页是第0个页面
    
    # 查找功能卡片容器
    cards_container = None
    for child in home_page.findChildren(type(home_page)):
        if hasattr(child, 'layout') and child.layout():
            layout = child.layout()
            if hasattr(layout, 'count') and layout.count() > 0:
                # 检查是否是网格布局
                from PyQt6.QtWidgets import QGridLayout
                if isinstance(layout, QGridLayout):
                    cards_container = child
                    break
    
    if cards_container:
        layout = cards_container.layout()
        card_count = 0
        for i in range(layout.count()):
            item = layout.itemAt(i)
            if item and item.widget():
                card_count += 1
        
        print(f"✅ 找到功能卡片容器，包含 {card_count} 个卡片")
        
        # 检查卡片标题
        from ui.main_window import FunctionCard
        cards = cards_container.findChildren(FunctionCard)
        print(f"✅ 找到 {len(cards)} 个功能卡片:")
        for card in cards:
            print(f"   - {card.title}: {card.description}")
    else:
        print("❌ 未找到功能卡片容器")
    
    # 显示窗口
    window.show()
    
    print("✅ 首页测试完成，程序正在运行...")
    return app.exec()

if __name__ == "__main__":
    test_homepage()
