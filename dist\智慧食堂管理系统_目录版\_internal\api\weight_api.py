#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商品重量提交API接口
Product Weight Submission API
"""

from typing import Dict, Any, Optional
import os
import requests
from .auth_api import AuthAPI


class WeightAPI(AuthAPI):
    """商品重量提交API类"""
    
    def __init__(self, base_url: str):
        """
        初始化重量提交API
        
        Args:
            base_url: API基础URL
        """
        super().__init__(base_url)
    
    def submit_weight(self, id: int, quantity: str, stock_mode: int, type: int = 0) -> Dict[str, Any]:
        """
        提交商品重量
        
        接口URL：https://st.pcylsoft.com:9006/st/steelyard/?op=weight
        Content-Type：application/x-www-form-urlencoded
        请求方式：post
        
        Args:
            id: 订单明细ID (Integer, 必填)
            quantity: 验收数量，多个重量用半角逗号分隔 (String, 必填)
            stock_mode: 入库类型，0=即入即出，1=先入后出 (Integer, 必填)
            type: 验收方式，0=全新验收，1=补验 (Integer, 可选，默认0)
            
        Returns:
            提交结果响应，包含以下字段：
            - code: 响应状态码 (200表示成功)
            - msg: 响应消息 ("success"表示成功)
            - total: 总数 (通常为0)
            
        Example:
            >>> api = WeightAPI("https://st.pcylsoft.com:9006/st/steelyard/")
            >>> api.set_access_token("your_token")
            >>> result = api.submit_weight(
            ...     id=1058706,
            ...     quantity="23.5,24.2,25.1",
            ...     stock_mode=0,
            ...     type=0
            ... )
            >>> print(result)
            {
                "code": 200,
                "msg": "success",
                "total": 0
            }
        """
        # 准备请求数据
        data = {
            'id': id,
            'quantity': quantity,
            'stock_mode': stock_mode,
            'type': type
        }
        
        # 发起POST请求
        # token放在Header中，Authorization字段
        return self.make_authenticated_request('POST', '?op=weight', data=data)
    
    def validate_weight_data(self, id: int, quantity: str, stock_mode: int) -> Dict[str, Any]:
        """
        验证重量数据的有效性
        
        Args:
            id: 订单明细ID
            quantity: 重量数据字符串
            stock_mode: 入库类型
            
        Returns:
            验证结果字典，包含：
            - valid: 是否有效 (bool)
            - errors: 错误列表 (list)
            - weights: 解析后的重量列表 (list)
        """
        errors = []
        weights = []
        
        # 验证订单明细ID
        if not isinstance(id, int) or id <= 0:
            errors.append("订单明细ID必须是正整数")
        
        # 验证入库类型
        if stock_mode not in [0, 1]:
            errors.append("入库类型必须是0(即入即出)或1(先入后出)")
        
        # 验证重量数据
        if not quantity or not isinstance(quantity, str):
            errors.append("重量数据不能为空")
        else:
            try:
                # 解析重量字符串
                weight_strs = quantity.split(',')
                for weight_str in weight_strs:
                    weight_str = weight_str.strip()
                    if not weight_str:
                        continue
                    
                    weight = float(weight_str)
                    if weight <= 0:
                        errors.append(f"重量值必须大于0: {weight_str}")
                    else:
                        weights.append(weight)
                
                if not weights:
                    errors.append("至少需要一个有效的重量值")
                    
            except ValueError as e:
                errors.append(f"重量数据格式错误: {str(e)}")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'weights': weights
        }
    
    def format_weight_quantity(self, weights: list) -> str:
        """
        格式化重量列表为API需要的字符串格式
        
        Args:
            weights: 重量值列表
            
        Returns:
            格式化后的重量字符串，用逗号分隔
            
        Example:
            >>> api.format_weight_quantity([23.5, 24.2, 25.1])
            "23.5,24.2,25.1"
        """
        if not weights:
            return ""
        
        # 格式化为2位小数并用逗号连接
        return ",".join([f"{weight:.2f}" for weight in weights])
    
    def parse_weight_quantity(self, quantity_str: str) -> list:
        """
        解析重量字符串为重量列表
        
        Args:
            quantity_str: 重量字符串，用逗号分隔
            
        Returns:
            重量值列表
            
        Example:
            >>> api.parse_weight_quantity("23.5,24.2,25.1")
            [23.5, 24.2, 25.1]
        """
        if not quantity_str:
            return []
        
        weights = []
        try:
            weight_strs = quantity_str.split(',')
            for weight_str in weight_strs:
                weight_str = weight_str.strip()
                if weight_str:
                    weight = float(weight_str)
                    weights.append(weight)
        except ValueError:
            pass  # 忽略无效的重量值
        
        return weights
    
    def get_stock_mode_text(self, stock_mode: int) -> str:
        """
        获取入库类型的文本描述
        
        Args:
            stock_mode: 入库类型代码
            
        Returns:
            入库类型文本描述
        """
        mode_map = {
            0: "即入即出",
            1: "先入后出"
        }
        return mode_map.get(stock_mode, f"未知类型({stock_mode})")
    
    def get_verification_type_text(self, type_code: int) -> str:
        """
        获取验收方式的文本描述
        
        Args:
            type_code: 验收方式代码
            
        Returns:
            验收方式文本描述
        """
        type_map = {
            0: "全新验收",
            1: "补验"
        }
        return type_map.get(type_code, f"未知方式({type_code})")

    def submit_picture(self, order_detail_id: int, batch: int, image_path: str) -> Dict[str, Any]:
        """
        提交商品图片

        Args:
            order_detail_id: 订单明细ID
            batch: 批次号（从0开始）
            image_path: 图片文件路径

        Returns:
            API响应结果
        """
        try:
            # 检查图片文件是否存在
            if not os.path.exists(image_path):
                return {
                    'success': False,
                    'message': f'图片文件不存在: {image_path}',
                    'data': None
                }

            # 检查文件大小（限制10MB）
            file_size = os.path.getsize(image_path)
            if file_size > 10 * 1024 * 1024:  # 10MB
                return {
                    'success': False,
                    'message': f'图片文件过大: {file_size / 1024 / 1024:.1f}MB，限制10MB',
                    'data': None
                }

            # 构建请求URL
            # 如果base_url已经包含/st/steelyard/，直接使用；否则添加路径
            if self.base_url.endswith('/st/steelyard/') or self.base_url.endswith('/st/steelyard'):
                url = self.base_url.rstrip('/') + '/'
            else:
                url = f"{self.base_url}/st/steelyard/"

            # 准备请求参数
            params = {'op': 'picture'}

            # 准备表单数据
            data = {
                'id': order_detail_id,
                'batch': batch
            }

            # 准备文件上传
            with open(image_path, 'rb') as f:
                files = {
                    'file': (os.path.basename(image_path), f, 'image/jpeg')
                }

                # 准备请求头（图片上传需要特殊处理）
                headers = {
                    'Authorization': self.access_token
                }
                # 注意：multipart/form-data的Content-Type会由requests自动设置

                # 发送POST请求
                response = requests.post(
                    url,
                    params=params,
                    data=data,
                    files=files,
                    headers=headers,
                    timeout=30
                )

            # 处理响应
            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('code') == 200:
                        return {
                            'success': True,
                            'message': '图片提交成功',
                            'data': {
                                'filename': result.get('msg', ''),
                                'order_detail_id': order_detail_id,
                                'batch': batch,
                                'original_path': image_path
                            }
                        }
                    else:
                        return {
                            'success': False,
                            'message': f"服务器返回错误: {result.get('msg', '未知错误')}",
                            'data': result
                        }
                except ValueError as e:
                    return {
                        'success': False,
                        'message': f'响应解析失败: {str(e)}',
                        'data': {'response_text': response.text}
                    }
            else:
                return {
                    'success': False,
                    'message': f'HTTP请求失败: {response.status_code}',
                    'data': {'response_text': response.text}
                }

        except requests.exceptions.Timeout:
            return {
                'success': False,
                'message': '请求超时，请检查网络连接',
                'data': None
            }
        except requests.exceptions.ConnectionError:
            return {
                'success': False,
                'message': '网络连接失败，请检查网络设置',
                'data': None
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'图片提交失败: {str(e)}',
                'data': None
            }
