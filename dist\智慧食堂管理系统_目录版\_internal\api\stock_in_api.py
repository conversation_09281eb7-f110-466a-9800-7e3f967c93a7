#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
入库管理API接口
Stock In Management API
"""

import json
import requests
from typing import Dict, Any, List, Optional
from .auth_api import AuthAPI


class StockInAPI(AuthAPI):
    """入库管理API类"""

    def __init__(self, base_url: str = "https://st.pcylsoft.com:9006"):
        """
        初始化入库管理API

        Args:
            base_url: API基础URL，默认为 https://st.pcylsoft.com:9006
        """
        super().__init__(base_url)

    def make_stock_in_authenticated_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """
        发起入库API的认证请求（使用Authorization header）

        Args:
            method: HTTP方法
            endpoint: 端点
            **kwargs: 其他请求参数

        Returns:
            请求结果字典
        """
        if not self.is_authenticated():
            return {
                'code': 401,
                'msg': '未认证，请先登录',
                'data': None
            }

        url = f"{self.base_url}/{endpoint.lstrip('/')}"

        # 为入库API设置特殊的headers
        headers = kwargs.get('headers', {})
        headers['Authorization'] = self.access_token
        kwargs['headers'] = headers

        try:
            response = self.session.request(method, url, timeout=10, **kwargs)

            # 检查是否是认证失败
            if response.status_code == 401:
                return {
                    'code': 401,
                    'msg': 'token已过期，请重新登录',
                    'data': None
                }

            response.raise_for_status()
            return response.json()

        except Exception as e:
            return {
                'code': 500,
                'msg': f'请求失败: {str(e)}',
                'data': None
            }
    
    def get_products_list(self) -> Dict[str, Any]:
        """
        获取货品列表
        
        接口URL：https://st.pcylsoft.com:9006/?op=stock_products
        Content-Type：application/x-www-form-urlencoded
        请求方式：post
        
        Returns:
            货品列表响应，包含以下字段：
            - code: 响应状态码 (200表示成功)
            - msg: 响应消息 ("success"表示成功)
            - data: 货品列表，每个货品包含：
                - code: 货品代码
                - name: 货品名称
                - unit: 货品单位
            - total: 货品总数
            
        Example:
            >>> api = StockInAPI("https://st.pcylsoft.com:9006")
            >>> api.set_access_token("your_token")
            >>> result = api.get_products_list()
            >>> print(result)
            {
                "code": 200,
                "msg": "success",
                "data": [
                    {
                        "code": "13010405",
                        "name": "小白菜",
                        "unit": "市斤"
                    },
                    {
                        "code": "13010406",
                        "name": "西红柿",
                        "unit": "市斤"
                    }
                ],
                "total": 2
            }
        """
        # 发起POST请求，op参数通过URL传递，token放在Header中的Authorization字段
        return self.make_stock_in_authenticated_request('POST', '?op=stock_products')
    
    def get_suppliers_list(self) -> Dict[str, Any]:
        """
        获取供应商列表
        
        接口URL：https://st.pcylsoft.com:9006/?op=stock_companys
        Content-Type：application/x-www-form-urlencoded
        请求方式：post
        
        Returns:
            供应商列表响应，包含以下字段：
            - code: 响应状态码 (200表示成功)
            - msg: 响应消息 ("success"表示成功)
            - data: 供应商列表，每个供应商包含：
                - code: 供应商代码
                - name: 供应商名称
            - total: 供应商总数
            
        Example:
            >>> api = StockInAPI("https://st.pcylsoft.com:9006")
            >>> api.set_access_token("your_token")
            >>> result = api.get_suppliers_list()
            >>> print(result)
            {
                "code": 200,
                "msg": "success",
                "data": [
                    {
                        "code": "S001",
                        "name": "蔬菜供应商A"
                    },
                    {
                        "code": "S002",
                        "name": "肉类供应商B"
                    }
                ],
                "total": 2
            }
        """
        # 发起POST请求，op参数通过URL传递，token放在Header中的Authorization字段
        return self.make_stock_in_authenticated_request('POST', '?op=stock_companys')
    
    def get_stock_in_index(self) -> Dict[str, Any]:
        """
        获取入库单号
        
        接口URL：https://st.pcylsoft.com:9006/?op=stockin_index
        Content-Type：application/x-www-form-urlencoded
        请求方式：post
        
        Returns:
            入库单号响应，包含以下字段：
            - code: 响应状态码 (200表示成功)
            - msg: 入库单号 (如"YI25073117941")
            - total: 总数 (通常为0)
            
        Example:
            >>> api = StockInAPI("https://st.pcylsoft.com:9006")
            >>> api.set_access_token("your_token")
            >>> result = api.get_stock_in_index()
            >>> print(result)
            {
                "code": 200,
                "msg": "YI25073117941",
                "total": 0
            }
        """
        # 发起POST请求，op参数通过URL传递，token放在Header中的Authorization字段
        return self.make_stock_in_authenticated_request('POST', '?op=stockin_index')

    def submit_stock_in(self, code: str, datetime: str, depot_code: str,
                       company_code: str, details: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        提交入库申请

        接口URL：https://st.pcylsoft.com:9006/?op=stockin_submit
        Content-Type：application/json
        请求方式：post

        Args:
            code: 入库单号（从get_stock_in_index接口获取）
            datetime: 入库时间，格式YYYY-MM-DD (如"2024-12-20")
            depot_code: 仓库代码 (如"S01")
            company_code: 供应商代码 (如"S001")
            details: 入库明细列表，每个明细包含：
                - code: 商品代码 (如"13010405")
                - quantity: 入库数量 (如"7.2")
                - unit: 单位 (如"市斤")
                - price: 价格 (如"5.50")
                - path: 图片地址 (如"23/638702976856623258.jpg;"，多张图片以分号;隔开)

        Returns:
            入库提交响应，包含以下字段：
            - code: 响应状态码 (200表示成功)
            - msg: 响应消息 (成功时返回相关信息)
            - total: 总数 (通常为0)

        Example:
            >>> api = StockInAPI("https://st.pcylsoft.com:9006")
            >>> api.set_access_token("your_token")
            >>> details = [
            ...     {
            ...         "code": "13010405",
            ...         "quantity": "7.2",
            ...         "unit": "市斤",
            ...         "price": "5.50",
            ...         "path": "23/638702976856623258.jpg;"
            ...     }
            ... ]
            >>> result = api.submit_stock_in(
            ...     code="YI25073117941",
            ...     datetime="2024-12-20",
            ...     depot_code="S01",
            ...     company_code="S001",
            ...     details=details
            ... )
            >>> print(result)
            {
                "code": 200,
                "msg": "入库成功",
                "total": 0
            }
        """
        # 构建请求数据
        request_data = {
            "code": code,
            "datetime": datetime,
            "depotcode": depot_code,
            "companycode": company_code,
            "details": details
        }

        # 发起POST请求，使用JSON格式
        return self._make_json_request('POST', '?op=stockin_submit', json_data=request_data)

    def _make_json_request(self, method: str, endpoint: str, params: Dict[str, Any] = None,
                          json_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        发起需要认证的JSON请求

        Args:
            method: HTTP方法
            endpoint: 端点
            params: URL参数
            json_data: JSON请求体数据

        Returns:
            请求结果字典
        """
        if not self.is_authenticated():
            return {
                'code': 401,
                'msg': '未认证，请先登录',
                'data': None
            }

        url = f"{self.base_url}/{endpoint.lstrip('/')}" if endpoint else self.base_url

        try:
            # 设置请求头
            headers = {
                'Authorization': self.access_token,
                'Content-Type': 'application/json'
            }

            response = self.session.request(
                method,
                url,
                params=params,
                json=json_data,
                headers=headers,
                timeout=10
            )

            # 检查是否是认证失败
            if response.status_code == 401:
                return {
                    'code': 401,
                    'msg': 'token已过期，请重新登录',
                    'data': None
                }

            response.raise_for_status()

            return response.json()

        except requests.exceptions.RequestException as e:
            return {
                'code': 500,
                'msg': f'网络请求失败: {str(e)}',
                'data': None
            }
        except Exception as e:
            return {
                'code': 500,
                'msg': f'请求过程中发生错误: {str(e)}',
                'data': None
            }

    def create_stock_in_detail(self, code: str, quantity: str, unit: str,
                              price: str, path: str = "") -> Dict[str, Any]:
        """
        创建入库明细数据

        Args:
            code: 商品代码
            quantity: 入库数量
            unit: 单位
            price: 价格
            path: 图片地址，多张图片以分号;隔开

        Returns:
            入库明细字典
        """
        return {
            "code": code,
            "quantity": quantity,
            "unit": unit,
            "price": price,
            "path": path
        }

    def validate_stock_in_data(self, code: str, datetime: str, depot_code: str,
                              company_code: str, details: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        验证入库数据的有效性

        Args:
            code: 入库单号
            datetime: 入库日期
            depot_code: 仓库代码
            company_code: 供应商代码
            details: 入库明细列表

        Returns:
            验证结果字典，包含：
            - valid: 是否有效 (bool)
            - errors: 错误列表 (list)
        """
        errors = []

        # 验证入库单号
        if not code or not isinstance(code, str):
            errors.append("入库单号不能为空")

        # 验证日期格式
        if not datetime or not isinstance(datetime, str):
            errors.append("入库日期不能为空")
        else:
            try:
                from datetime import datetime as dt
                dt.strptime(datetime, "%Y-%m-%d")
            except ValueError:
                errors.append("入库日期格式错误，应为YYYY-MM-DD")

        # 验证仓库代码
        if not depot_code or not isinstance(depot_code, str):
            errors.append("仓库代码不能为空")

        # 验证供应商代码
        if not company_code or not isinstance(company_code, str):
            errors.append("供应商代码不能为空")

        # 验证明细列表
        if not details or not isinstance(details, list):
            errors.append("入库明细列表不能为空")
        else:
            for i, detail in enumerate(details):
                if not isinstance(detail, dict):
                    errors.append(f"明细{i+1}格式错误")
                    continue

                # 验证商品代码
                if not detail.get('code'):
                    errors.append(f"明细{i+1}商品代码不能为空")

                # 验证数量
                quantity = detail.get('quantity')
                if not quantity:
                    errors.append(f"明细{i+1}数量不能为空")
                else:
                    try:
                        float(quantity)
                    except (ValueError, TypeError):
                        errors.append(f"明细{i+1}数量格式错误")

                # 验证单位
                if not detail.get('unit'):
                    errors.append(f"明细{i+1}单位不能为空")

                # 验证价格
                price = detail.get('price')
                if not price:
                    errors.append(f"明细{i+1}价格不能为空")
                else:
                    try:
                        float(price)
                    except (ValueError, TypeError):
                        errors.append(f"明细{i+1}价格格式错误")

        return {
            'valid': len(errors) == 0,
            'errors': errors
        }

    def get_depot_list(self) -> Dict[str, Any]:
        """
        获取仓库列表（复用出库模块的仓库接口）

        Returns:
            仓库列表响应
        """
        # 这里可以复用出库模块的仓库接口，或者根据需要调用专门的入库仓库接口
        return self.make_stock_in_authenticated_request('POST', '?op=stock_depot')

    def format_stock_in_summary(self, code: str, datetime: str, depot_code: str,
                               company_code: str, details: List[Dict[str, Any]]) -> str:
        """
        格式化入库单摘要信息

        Args:
            code: 入库单号
            datetime: 入库日期
            depot_code: 仓库代码
            company_code: 供应商代码
            details: 入库明细列表

        Returns:
            格式化的摘要字符串
        """
        total_items = len(details)
        total_quantity = sum(float(detail.get('quantity', 0)) for detail in details)
        total_amount = sum(float(detail.get('quantity', 0)) * float(detail.get('price', 0)) for detail in details)

        summary = f"""
入库单摘要：
单号：{code}
日期：{datetime}
仓库：{depot_code}
供应商：{company_code}
商品种类：{total_items}
总数量：{total_quantity:.2f}
总金额：{total_amount:.2f}元
        """.strip()

        return summary
