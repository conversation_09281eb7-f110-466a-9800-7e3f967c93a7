('C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\build\\MaiCui\\智慧食堂管理系统.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\build\\MaiCui\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\build\\MaiCui\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\build\\MaiCui\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\build\\MaiCui\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\build\\MaiCui\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\build\\MaiCui\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\python\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt6',
   'D:\\python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt6.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('main',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\main.py',
   'PYSOURCE'),
  ('python313.dll', 'D:\\python\\python313.dll', 'BINARY'),
  ('PyQt6\\Qt6\\bin\\opengl32sw.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\multimedia\\ffmpegmediaplugin.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\multimedia\\ffmpegmediaplugin.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\multimedia\\windowsmediaplugin.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\multimedia\\windowsmediaplugin.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\tls\\qopensslbackend.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\tls\\qopensslbackend.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\tls\\qcertonlybackend.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\tls\\qcertonlybackend.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\networkinformation\\qnetworklistmanager.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\networkinformation\\qnetworklistmanager.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\tls\\qschannelbackend.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\tls\\qschannelbackend.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'D:\\python\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'D:\\python\\Lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('cv2\\opencv_videoio_ffmpeg4120_64.dll',
   'D:\\python\\Lib\\site-packages\\cv2\\opencv_videoio_ffmpeg4120_64.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qgif.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qsvg.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qtiff.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qtga.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qwindows.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qwebp.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qpdf.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qpdf.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qjpeg.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\iconengines\\qsvgicon.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qoffscreen.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qwbmp.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qico.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qminimal.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qicns.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'BINARY'),
  ('select.pyd', 'D:\\python\\DLLs\\select.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\python\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'D:\\python\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'D:\\python\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'D:\\python\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'D:\\python\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\python\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\python\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\python\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'D:\\python\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'D:\\python\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('PyQt6\\QtMultimediaWidgets.pyd',
   'D:\\python\\Lib\\site-packages\\PyQt6\\QtMultimediaWidgets.pyd',
   'EXTENSION'),
  ('PyQt6\\QtMultimedia.pyd',
   'D:\\python\\Lib\\site-packages\\PyQt6\\QtMultimedia.pyd',
   'EXTENSION'),
  ('PyQt6\\QtPrintSupport.pyd',
   'D:\\python\\Lib\\site-packages\\PyQt6\\QtPrintSupport.pyd',
   'EXTENSION'),
  ('PyQt6\\QtNetwork.pyd',
   'D:\\python\\Lib\\site-packages\\PyQt6\\QtNetwork.pyd',
   'EXTENSION'),
  ('PyQt6\\sip.cp313-win_amd64.pyd',
   'D:\\python\\Lib\\site-packages\\PyQt6\\sip.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp313-win_amd64.pyd',
   'D:\\python\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp313-win_amd64.pyd',
   'D:\\python\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'D:\\python\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('unicodedata.pyd', 'D:\\python\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('charset_normalizer\\md.cp313-win_amd64.pyd',
   'D:\\python\\Lib\\site-packages\\charset_normalizer\\md.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_wmi.pyd', 'D:\\python\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp313-win_amd64.pyd',
   'D:\\python\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd', 'D:\\python\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'D:\\python\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('numpy\\random\\mtrand.cp313-win_amd64.pyd',
   'D:\\python\\Lib\\site-packages\\numpy\\random\\mtrand.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp313-win_amd64.pyd',
   'D:\\python\\Lib\\site-packages\\numpy\\random\\_sfc64.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp313-win_amd64.pyd',
   'D:\\python\\Lib\\site-packages\\numpy\\random\\_philox.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp313-win_amd64.pyd',
   'D:\\python\\Lib\\site-packages\\numpy\\random\\_pcg64.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp313-win_amd64.pyd',
   'D:\\python\\Lib\\site-packages\\numpy\\random\\_mt19937.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp313-win_amd64.pyd',
   'D:\\python\\Lib\\site-packages\\numpy\\random\\bit_generator.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp313-win_amd64.pyd',
   'D:\\python\\Lib\\site-packages\\numpy\\random\\_generator.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp313-win_amd64.pyd',
   'D:\\python\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp313-win_amd64.pyd',
   'D:\\python\\Lib\\site-packages\\numpy\\random\\_common.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp313-win_amd64.pyd',
   'D:\\python\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('cv2\\cv2.pyd', 'D:\\python\\Lib\\site-packages\\cv2\\cv2.pyd', 'EXTENSION'),
  ('PyQt6\\QtGui.pyd',
   'D:\\python\\Lib\\site-packages\\PyQt6\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt6\\QtCore.pyd',
   'D:\\python\\Lib\\site-packages\\PyQt6\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt6\\QtWidgets.pyd',
   'D:\\python\\Lib\\site-packages\\PyQt6\\QtWidgets.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll', 'D:\\python\\VCRUNTIME140.dll', 'BINARY'),
  ('PyQt6\\Qt6\\bin\\avutil-59.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\avutil-59.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Multimedia.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Multimedia.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Gui.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Gui.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Core.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Core.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll', 'D:\\python\\VCRUNTIME140_1.dll', 'BINARY'),
  ('PyQt6\\Qt6\\bin\\swresample-5.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\swresample-5.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\swscale-8.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\swscale-8.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\avformat-61.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\avformat-61.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\avcodec-61.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\avcodec-61.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Network.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Network.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Svg.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Svg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Pdf.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Pdf.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Widgets.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Widgets.dll',
   'BINARY'),
  ('libssl-3.dll', 'D:\\python\\DLLs\\libssl-3.dll', 'BINARY'),
  ('libcrypto-3.dll', 'D:\\python\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('libffi-8.dll', 'D:\\python\\DLLs\\libffi-8.dll', 'BINARY'),
  ('python3.dll', 'D:\\python\\python3.dll', 'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6MultimediaWidgets.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6MultimediaWidgets.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6PrintSupport.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6PrintSupport.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140_2.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140_2.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\VCRUNTIME140.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\VCRUNTIME140_1.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140_1.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('api\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\__init__.py',
   'DATA'),
  ('api\\__pycache__\\__init__.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('api\\__pycache__\\auth_api.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\__pycache__\\auth_api.cpython-313.pyc',
   'DATA'),
  ('api\\__pycache__\\canteen_api.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\__pycache__\\canteen_api.cpython-313.pyc',
   'DATA'),
  ('api\\__pycache__\\order_api.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\__pycache__\\order_api.cpython-313.pyc',
   'DATA'),
  ('api\\__pycache__\\stock_api.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\__pycache__\\stock_api.cpython-313.pyc',
   'DATA'),
  ('api\\__pycache__\\stock_in_api.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\__pycache__\\stock_in_api.cpython-313.pyc',
   'DATA'),
  ('api\\__pycache__\\weight_api.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\__pycache__\\weight_api.cpython-313.pyc',
   'DATA'),
  ('api\\auth_api.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\auth_api.py',
   'DATA'),
  ('api\\canteen_api.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\canteen_api.py',
   'DATA'),
  ('api\\order_api.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\order_api.py',
   'DATA'),
  ('api\\stock_api.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\stock_api.py',
   'DATA'),
  ('api\\stock_in_api.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\stock_in_api.py',
   'DATA'),
  ('api\\weight_api.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\weight_api.py',
   'DATA'),
  ('config\\GrilmorphismUI.json',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\config\\GrilmorphismUI.json',
   'DATA'),
  ('config\\__pycache__\\settings.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\config\\__pycache__\\settings.cpython-313.pyc',
   'DATA'),
  ('config\\app_config.json',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\config\\app_config.json',
   'DATA'),
  ('config\\auth_storage.json',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\config\\auth_storage.json',
   'DATA'),
  ('config\\settings.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\config\\settings.py',
   'DATA'),
  ('photos\\auto_photo_20250730_183544.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\auto_photo_20250730_183544.jpg',
   'DATA'),
  ('photos\\auto_photo_20250731_144007.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\auto_photo_20250731_144007.jpg',
   'DATA'),
  ('photos\\photo_20250730_183015.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250730_183015.jpg',
   'DATA'),
  ('photos\\photo_20250730_184018.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250730_184018.jpg',
   'DATA'),
  ('photos\\photo_20250730_184020.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250730_184020.jpg',
   'DATA'),
  ('photos\\photo_20250730_184215.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250730_184215.jpg',
   'DATA'),
  ('photos\\photo_20250731_081840.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250731_081840.jpg',
   'DATA'),
  ('photos\\photo_20250731_082920.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250731_082920.jpg',
   'DATA'),
  ('photos\\photo_20250731_085629.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250731_085629.jpg',
   'DATA'),
  ('photos\\photo_20250731_101724.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250731_101724.jpg',
   'DATA'),
  ('photos\\photo_20250731_101726.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250731_101726.jpg',
   'DATA'),
  ('photos\\photo_20250731_101728.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250731_101728.jpg',
   'DATA'),
  ('photos\\photo_20250731_111513.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250731_111513.jpg',
   'DATA'),
  ('photos\\photo_20250731_144446.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250731_144446.jpg',
   'DATA'),
  ('photos\\photo_20250731_153313.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250731_153313.jpg',
   'DATA'),
  ('photos\\photo_20250731_170444.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250731_170444.jpg',
   'DATA'),
  ('photos\\photo_20250801_194325.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_194325.jpg',
   'DATA'),
  ('photos\\photo_20250801_222155.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_222155.jpg',
   'DATA'),
  ('photos\\photo_20250801_222201.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_222201.jpg',
   'DATA'),
  ('photos\\photo_20250801_222205.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_222205.jpg',
   'DATA'),
  ('photos\\photo_20250801_222220.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_222220.jpg',
   'DATA'),
  ('photos\\photo_20250801_222440.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_222440.jpg',
   'DATA'),
  ('photos\\photo_20250801_222456.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_222456.jpg',
   'DATA'),
  ('photos\\photo_20250801_222505.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_222505.jpg',
   'DATA'),
  ('photos\\photo_20250801_222510.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_222510.jpg',
   'DATA'),
  ('photos\\photo_20250801_222518.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_222518.jpg',
   'DATA'),
  ('photos\\photo_20250801_223922.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_223922.jpg',
   'DATA'),
  ('photos\\photo_20250801_225011.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_225011.jpg',
   'DATA'),
  ('ui\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\__init__.py',
   'DATA'),
  ('ui\\__pycache__\\__init__.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('ui\\__pycache__\\global_serial_manager.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\__pycache__\\global_serial_manager.cpython-313.pyc',
   'DATA'),
  ('ui\\__pycache__\\login_window.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\__pycache__\\login_window.cpython-313.pyc',
   'DATA'),
  ('ui\\__pycache__\\main_window.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\__pycache__\\main_window.cpython-313.pyc',
   'DATA'),
  ('ui\\__pycache__\\serial_manager.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\__pycache__\\serial_manager.cpython-313.pyc',
   'DATA'),
  ('ui\\__pycache__\\styles.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\__pycache__\\styles.cpython-313.pyc',
   'DATA'),
  ('ui\\dialogs\\__pycache__\\serial_config_dialog.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\dialogs\\__pycache__\\serial_config_dialog.cpython-313.pyc',
   'DATA'),
  ('ui\\dialogs\\serial_config_dialog.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\dialogs\\serial_config_dialog.py',
   'DATA'),
  ('ui\\global_serial_manager.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\global_serial_manager.py',
   'DATA'),
  ('ui\\login_window.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\login_window.py',
   'DATA'),
  ('ui\\main_window.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\main_window.py',
   'DATA'),
  ('ui\\modules\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\__init__.py',
   'DATA'),
  ('ui\\modules\\__pycache__\\__init__.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('ui\\modules\\__pycache__\\camera_module.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\__pycache__\\camera_module.cpython-313.pyc',
   'DATA'),
  ('ui\\modules\\__pycache__\\order_module.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\__pycache__\\order_module.cpython-313.pyc',
   'DATA'),
  ('ui\\modules\\__pycache__\\recipe_module.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\__pycache__\\recipe_module.cpython-313.pyc',
   'DATA'),
  ('ui\\modules\\__pycache__\\stock_in_module.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\__pycache__\\stock_in_module.cpython-313.pyc',
   'DATA'),
  ('ui\\modules\\__pycache__\\stock_out_module.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\__pycache__\\stock_out_module.cpython-313.pyc',
   'DATA'),
  ('ui\\modules\\__pycache__\\weight_submission_module.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\__pycache__\\weight_submission_module.cpython-313.pyc',
   'DATA'),
  ('ui\\modules\\camera_module.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\camera_module.py',
   'DATA'),
  ('ui\\modules\\order_module.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\order_module.py',
   'DATA'),
  ('ui\\modules\\recipe_module.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\recipe_module.py',
   'DATA'),
  ('ui\\modules\\stock_in_module.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\stock_in_module.py',
   'DATA'),
  ('ui\\modules\\stock_out_module.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\stock_out_module.py',
   'DATA'),
  ('ui\\modules\\weight_submission_module.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\weight_submission_module.py',
   'DATA'),
  ('ui\\serial_manager.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\serial_manager.py',
   'DATA'),
  ('ui\\styles.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\styles.py',
   'DATA'),
  ('utils\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\utils\\__init__.py',
   'DATA'),
  ('utils\\__pycache__\\__init__.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\utils\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('utils\\__pycache__\\auth_manager.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\utils\\__pycache__\\auth_manager.cpython-313.pyc',
   'DATA'),
  ('utils\\auth_manager.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\utils\\auth_manager.py',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_ko.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_zh_TW.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_da.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_it.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_en.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_ru.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_de.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_pl.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_nn.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_fr.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_pt_BR.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_tr.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_sk.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_uk.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_ja.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_hu.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_fi.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_fi.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_cs.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_bg.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_ca.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_ka.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_es.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_fa.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_fa.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_zh_CN.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_ar.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_nl.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_hr.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_hr.qm',
   'DATA'),
  ('cv2\\config-3.py',
   'D:\\python\\Lib\\site-packages\\cv2\\config-3.py',
   'DATA'),
  ('cv2\\load_config_py3.py',
   'D:\\python\\Lib\\site-packages\\cv2\\load_config_py3.py',
   'DATA'),
  ('cv2\\config.py', 'D:\\python\\Lib\\site-packages\\cv2\\config.py', 'DATA'),
  ('certifi\\py.typed',
   'D:\\python\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\python\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fr.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ja.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ar.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_nn.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sl.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ka.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_hu.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fa.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_uk.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_sk.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ja.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_zh_CN.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_gd.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_tr.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_hu.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_nn.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ko.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_zh_TW.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pt_BR.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_hr.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_es.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ko.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_lv.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_es.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_tr.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ka.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fa.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fa.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_es.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_cs.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_hr.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pt_PT.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pt_PT.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_sk.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_it.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_tr.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ka.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_bg.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_bg.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ko.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_pt_BR.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ca.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fi.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ar.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_gl.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_pl.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ru.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_en.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_hr.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sk.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ru.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_pl.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_zh_CN.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_nl.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_da.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_it.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_bg.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_de.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sv.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_gd.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_gl.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_lt.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ja.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ca.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_pt_BR.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fr.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_da.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_da.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_he.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_hu.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ru.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_nn.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_en.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_zh_TW.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_zh_CN.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_he.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_uk.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_sl.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_uk.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pl.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_de.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_nl.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_nl.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_cs.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_it.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ca.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_lv.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fi.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ar.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_cs.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_fr.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_zh_TW.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_en.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_de.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_de.qm',
   'DATA'),
  ('numpy-2.2.6.dist-info\\RECORD',
   'D:\\python\\Lib\\site-packages\\numpy-2.2.6.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.2.6.dist-info\\DELVEWHEEL',
   'D:\\python\\Lib\\site-packages\\numpy-2.2.6.dist-info\\DELVEWHEEL',
   'DATA'),
  ('numpy-2.2.6.dist-info\\METADATA',
   'D:\\python\\Lib\\site-packages\\numpy-2.2.6.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.2.6.dist-info\\entry_points.txt',
   'D:\\python\\Lib\\site-packages\\numpy-2.2.6.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\WHEEL',
   'D:\\python\\Lib\\site-packages\\numpy-2.2.6.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.2.6.dist-info\\LICENSE.txt',
   'D:\\python\\Lib\\site-packages\\numpy-2.2.6.dist-info\\LICENSE.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\REQUESTED',
   'D:\\python\\Lib\\site-packages\\numpy-2.2.6.dist-info\\REQUESTED',
   'DATA'),
  ('numpy-2.2.6.dist-info\\INSTALLER',
   'D:\\python\\Lib\\site-packages\\numpy-2.2.6.dist-info\\INSTALLER',
   'DATA'),
  ('cv2\\__init__.py',
   'D:\\python\\Lib\\site-packages\\cv2\\__init__.py',
   'DATA'),
  ('cv2\\version.py',
   'D:\\python\\Lib\\site-packages\\cv2\\version.py',
   'DATA'),
  ('cv2\\utils\\__init__.py',
   'D:\\python\\Lib\\site-packages\\cv2\\utils\\__init__.py',
   'DATA'),
  ('cv2\\typing\\__init__.py',
   'D:\\python\\Lib\\site-packages\\cv2\\typing\\__init__.py',
   'DATA'),
  ('cv2\\misc\\version.py',
   'D:\\python\\Lib\\site-packages\\cv2\\misc\\version.py',
   'DATA'),
  ('cv2\\misc\\__init__.py',
   'D:\\python\\Lib\\site-packages\\cv2\\misc\\__init__.py',
   'DATA'),
  ('cv2\\mat_wrapper\\__init__.py',
   'D:\\python\\Lib\\site-packages\\cv2\\mat_wrapper\\__init__.py',
   'DATA'),
  ('cv2\\gapi\\__init__.py',
   'D:\\python\\Lib\\site-packages\\cv2\\gapi\\__init__.py',
   'DATA'),
  ('cv2\\data\\__init__.py',
   'D:\\python\\Lib\\site-packages\\cv2\\data\\__init__.py',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\build\\MaiCui\\base_library.zip',
   'DATA')],
 'python313.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
