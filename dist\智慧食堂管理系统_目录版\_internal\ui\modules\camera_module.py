#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
摄像头模块
Camera Module for Weight Submission
"""

import cv2
import os
import time
from datetime import datetime
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QFrame, QMessageBox)
from PyQt6.QtCore import QTimer, pyqtSignal, QThread, pyqtSlot
from PyQt6.QtGui import QImage, QPixmap, QFont
from PyQt6.QtCore import Qt


class CameraWorker(QThread):
    """摄像头工作线程"""
    
    frame_ready = pyqtSignal(QImage)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, camera_index=0):
        super().__init__()
        self.camera_index = camera_index
        self.running = False
        self.cap = None
    
    def run(self):
        """运行摄像头捕获"""
        try:
            # 初始化摄像头
            self.cap = cv2.VideoCapture(self.camera_index)
            if not self.cap.isOpened():
                self.error_occurred.emit(f"无法打开摄像头 {self.camera_index}")
                return
            
            # 设置摄像头参数
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 320)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 240)
            self.cap.set(cv2.CAP_PROP_FPS, 15)
            
            self.running = True
            
            while self.running:
                ret, frame = self.cap.read()
                if ret:
                    # 转换为RGB格式
                    rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    h, w, ch = rgb_frame.shape
                    bytes_per_line = ch * w
                    
                    # 创建QImage
                    qt_image = QImage(rgb_frame.data, w, h, bytes_per_line, QImage.Format.Format_RGB888)
                    self.frame_ready.emit(qt_image)
                else:
                    self.error_occurred.emit("摄像头读取失败")
                    break
                
                self.msleep(66)  # ~15 FPS
                
        except Exception as e:
            self.error_occurred.emit(f"摄像头错误: {str(e)}")
        finally:
            if self.cap:
                self.cap.release()
    
    def stop(self):
        """停止摄像头"""
        self.running = False
        self.wait()
        if self.cap:
            self.cap.release()
    
    def capture_photo(self, save_path: str) -> bool:
        """拍照保存"""
        if self.cap and self.cap.isOpened():
            ret, frame = self.cap.read()
            if ret:
                # 保存图片
                success = cv2.imwrite(save_path, frame)
                return success
        return False


class CameraModule(QWidget):
    """摄像头模块"""
    
    photo_captured = pyqtSignal(str)  # 拍照完成信号，传递图片路径
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.camera_worker = None
        self.is_camera_active = False
        self.photos_dir = "photos"  # 照片保存目录
        
        # 确保照片目录存在
        os.makedirs(self.photos_dir, exist_ok=True)
        
        self.init_ui()
        self.setup_connections()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(6)
        
        # 摄像头显示区域
        self.create_camera_display(layout)
        
        # 控制按钮区域
        self.create_control_buttons(layout)
        
        # 状态显示
        self.create_status_display(layout)
    
    def create_camera_display(self, parent_layout):
        """创建摄像头显示区域"""
        # 摄像头画面框架
        self.camera_frame = QFrame()
        self.camera_frame.setStyleSheet("""
            QFrame {
                background: rgba(0, 0, 0, 0.8);
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                min-height: 180px;
                max-height: 200px;
            }
        """)
        
        camera_layout = QVBoxLayout(self.camera_frame)
        camera_layout.setContentsMargins(4, 4, 4, 4)
        
        # 摄像头画面标签
        self.camera_label = QLabel("📷 摄像头未启动")
        self.camera_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.7);
                font-size: 12px;
                text-align: center;
                background: transparent;
                border: none;
                min-height: 160px;
            }
        """)
        self.camera_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.camera_label.setWordWrap(True)
        self.camera_label.setScaledContents(True)
        camera_layout.addWidget(self.camera_label)
        
        parent_layout.addWidget(self.camera_frame)
    
    def create_control_buttons(self, parent_layout):
        """创建控制按钮"""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(4)
        
        # 启动/停止摄像头按钮
        self.toggle_camera_btn = QPushButton("📹 启动")
        self.toggle_camera_btn.setFont(QFont("Inter", 9, QFont.Weight.Medium))
        self.toggle_camera_btn.setStyleSheet("""
            QPushButton {
                background: rgba(34, 197, 94, 0.2);
                border: 1px solid rgba(34, 197, 94, 0.3);
                border-radius: 4px;
                padding: 4px 8px;
                color: white;
                font-size: 9px;
                min-height: 24px;
            }
            QPushButton:hover {
                background: rgba(34, 197, 94, 0.3);
            }
            QPushButton:pressed {
                background: rgba(34, 197, 94, 0.4);
            }
        """)
        button_layout.addWidget(self.toggle_camera_btn)
        
        # 拍照按钮
        self.capture_btn = QPushButton("📸 拍照")
        self.capture_btn.setFont(QFont("Inter", 9, QFont.Weight.Medium))
        self.capture_btn.setStyleSheet("""
            QPushButton {
                background: rgba(59, 130, 246, 0.2);
                border: 1px solid rgba(59, 130, 246, 0.3);
                border-radius: 4px;
                padding: 4px 8px;
                color: white;
                font-size: 9px;
                min-height: 24px;
            }
            QPushButton:hover:enabled {
                background: rgba(59, 130, 246, 0.3);
            }
            QPushButton:pressed {
                background: rgba(59, 130, 246, 0.4);
            }
            QPushButton:disabled {
                background: rgba(255, 255, 255, 0.05);
                color: rgba(255, 255, 255, 0.3);
                border: 1px solid rgba(255, 255, 255, 0.1);
            }
        """)
        self.capture_btn.setEnabled(False)
        button_layout.addWidget(self.capture_btn)
        
        parent_layout.addLayout(button_layout)
    
    def create_status_display(self, parent_layout):
        """创建状态显示"""
        self.status_label = QLabel("📷 摄像头就绪")
        self.status_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.7);
                font-size: 8px;
                text-align: center;
                padding: 2px;
                min-height: 12px;
            }
        """)
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setWordWrap(True)
        parent_layout.addWidget(self.status_label)
    
    def setup_connections(self):
        """设置信号连接"""
        self.toggle_camera_btn.clicked.connect(self.toggle_camera)
        self.capture_btn.clicked.connect(self.capture_photo)
    
    def toggle_camera(self):
        """切换摄像头状态"""
        if not self.is_camera_active:
            self.start_camera()
        else:
            self.stop_camera()
    
    def start_camera(self):
        """启动摄像头"""
        try:
            self.camera_worker = CameraWorker(camera_index=0)
            self.camera_worker.frame_ready.connect(self.update_frame)
            self.camera_worker.error_occurred.connect(self.handle_camera_error)
            self.camera_worker.start()
            
            self.is_camera_active = True
            self.toggle_camera_btn.setText("⏹️ 停止")
            self.toggle_camera_btn.setStyleSheet("""
                QPushButton {
                    background: rgba(239, 68, 68, 0.2);
                    border: 1px solid rgba(239, 68, 68, 0.3);
                    border-radius: 4px;
                    padding: 4px 8px;
                    color: white;
                    font-size: 9px;
                    min-height: 24px;
                }
                QPushButton:hover {
                    background: rgba(239, 68, 68, 0.3);
                }
            """)
            self.capture_btn.setEnabled(True)
            self.status_label.setText("🟢 摄像头运行中")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动摄像头失败: {str(e)}")
    
    def stop_camera(self):
        """停止摄像头"""
        if self.camera_worker:
            self.camera_worker.stop()
            self.camera_worker = None
        
        self.is_camera_active = False
        self.toggle_camera_btn.setText("📹 启动")
        self.toggle_camera_btn.setStyleSheet("""
            QPushButton {
                background: rgba(34, 197, 94, 0.2);
                border: 1px solid rgba(34, 197, 94, 0.3);
                border-radius: 4px;
                padding: 4px 8px;
                color: white;
                font-size: 9px;
                min-height: 24px;
            }
            QPushButton:hover {
                background: rgba(34, 197, 94, 0.3);
            }
        """)
        self.capture_btn.setEnabled(False)
        self.camera_label.setText("📷 摄像头已停止")
        self.status_label.setText("⚫ 摄像头已停止")
    
    @pyqtSlot(QImage)
    def update_frame(self, image):
        """更新摄像头画面"""
        # 缩放图像以适应标签大小
        pixmap = QPixmap.fromImage(image)
        scaled_pixmap = pixmap.scaled(
            self.camera_label.size(), 
            Qt.AspectRatioMode.KeepAspectRatio, 
            Qt.TransformationMode.SmoothTransformation
        )
        self.camera_label.setPixmap(scaled_pixmap)
    
    @pyqtSlot(str)
    def handle_camera_error(self, error_msg):
        """处理摄像头错误"""
        self.status_label.setText(f"❌ {error_msg}")
        self.stop_camera()
        QMessageBox.warning(self, "摄像头错误", error_msg)
    
    def capture_photo(self):
        """拍照"""
        if not self.camera_worker:
            return
        
        try:
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"photo_{timestamp}.jpg"
            filepath = os.path.join(self.photos_dir, filename)
            
            # 拍照保存
            if self.camera_worker.capture_photo(filepath):
                self.status_label.setText(f"📸 拍照成功: {filename}")
                self.photo_captured.emit(filepath)
            else:
                self.status_label.setText("❌ 拍照失败")
                QMessageBox.warning(self, "拍照失败", "无法保存照片")
                
        except Exception as e:
            error_msg = f"拍照失败: {str(e)}"
            self.status_label.setText(f"❌ {error_msg}")
            QMessageBox.critical(self, "拍照错误", error_msg)
    
    def cleanup(self):
        """清理资源"""
        self.stop_camera()
