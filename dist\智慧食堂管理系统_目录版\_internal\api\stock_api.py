#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
库存管理API接口
Stock Management API
"""

import json
import requests
from typing import Dict, Any, List, Optional
from .auth_api import AuthAPI


class StockAPI(AuthAPI):
    """库存管理API类"""

    def __init__(self, base_url: str):
        """
        初始化库存管理API

        Args:
            base_url: API基础URL
        """
        super().__init__(base_url)

    def make_stock_authenticated_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """
        发起库存API的认证请求（使用access_token header）

        Args:
            method: HTTP方法
            endpoint: 端点
            **kwargs: 其他请求参数

        Returns:
            请求结果字典
        """
        if not self.is_authenticated():
            return {
                'code': 401,
                'msg': '未认证，请先登录',
                'data': None
            }

        url = f"{self.base_url}/{endpoint.lstrip('/')}"

        # 为库存API设置特殊的headers
        headers = kwargs.get('headers', {})
        headers['access_token'] = self.access_token
        kwargs['headers'] = headers

        try:
            response = self.session.request(method, url, timeout=10, **kwargs)

            # 检查是否是认证失败
            if response.status_code == 401:
                return {
                    'code': 401,
                    'msg': 'token已过期，请重新登录',
                    'data': None
                }

            response.raise_for_status()
            return response.json()

        except Exception as e:
            return {
                'code': 500,
                'msg': f'请求失败: {str(e)}',
                'data': None
            }
    
    def get_depot_list(self) -> Dict[str, Any]:
        """
        获取仓库列表
        
        接口URL：https://st.pcylsoft.com:9006/st/steelyard/?op=stock_depot
        Content-Type：application/x-www-form-urlencoded
        请求方式：post
        
        Returns:
            仓库列表响应，包含以下字段：
            - code: 响应状态码 (200表示成功)
            - msg: 响应消息 ("success"表示成功)
            - data: 仓库列表，每个仓库包含：
                - code: 仓库代码 (如"S01")
                - name: 仓库名称 (如"食堂仓库")
                - number: 仓库编号 (如"01")
            - total: 仓库总数
            
        Example:
            >>> api = StockAPI("https://st.pcylsoft.com:9006/st/steelyard/")
            >>> api.set_access_token("your_token")
            >>> result = api.get_depot_list()
            >>> print(result)
            {
                "code": 200,
                "msg": "success",
                "data": [
                    {
                        "code": "S01",
                        "name": "食堂仓库",
                        "number": "01"
                    },
                    {
                        "code": "S02",
                        "name": "调料仓库",
                        "number": "02"
                    }
                ],
                "total": 2
            }
        """
        # 发起POST请求，op参数通过URL传递，token放在Header中的access_token字段
        return self.make_stock_authenticated_request('POST', '?op=stock_depot')
    
    def get_project_list(self) -> Dict[str, Any]:
        """
        获取项目列表
        
        接口URL：https://st.pcylsoft.com:9006/st/steelyard/?op=stock_project
        Content-Type：application/x-www-form-urlencoded
        请求方式：post
        
        Returns:
            项目列表响应，包含以下字段：
            - code: 响应状态码 (200表示成功)
            - msg: 响应消息 ("success"表示成功)
            - data: 项目列表，每个项目包含：
                - code: 项目代码 (如"P0001")
                - name: 项目名称 (如"非营养餐")
            - total: 项目总数
            
        Example:
            >>> api = StockAPI("https://st.pcylsoft.com:9006/st/steelyard/")
            >>> api.set_access_token("your_token")
            >>> result = api.get_project_list()
            >>> print(result)
            {
                "code": 200,
                "msg": "success",
                "data": [
                    {
                        "code": "P0001",
                        "name": "非营养餐"
                    },
                    {
                        "code": "P0002",
                        "name": "营养餐"
                    }
                ],
                "total": 2
            }
        """
        # 发起POST请求，op参数通过URL传递，token放在Header中的access_token字段
        return self.make_stock_authenticated_request('POST', '?op=stock_project')
    
    def get_meal_time_list(self) -> Dict[str, Any]:
        """
        获取餐别列表
        
        接口URL：https://st.pcylsoft.com:9006/st/steelyard/?op=stock_time
        Content-Type：application/x-www-form-urlencoded
        请求方式：post
        
        Returns:
            餐别列表响应，包含以下字段：
            - code: 响应状态码 (200表示成功)
            - msg: 响应消息 ("success"表示成功)
            - data: 餐别列表，每个餐别包含：
                - value: 餐别值 (如1, 2, 4, 8, 16, 32)
                - name: 餐别名称 (如"早餐", "早点", "午餐", "午点", "晚餐", "晚点")
            - total: 餐别总数
            
        Example:
            >>> api = StockAPI("https://st.pcylsoft.com:9006/st/steelyard/")
            >>> api.set_access_token("your_token")
            >>> result = api.get_meal_time_list()
            >>> print(result)
            {
                "code": 200,
                "msg": "success",
                "data": [
                    {
                        "value": 1,
                        "name": "早餐"
                    },
                    {
                        "value": 2,
                        "name": "早点"
                    },
                    {
                        "value": 4,
                        "name": "午餐"
                    },
                    {
                        "value": 8,
                        "name": "午点"
                    },
                    {
                        "value": 16,
                        "name": "晚餐"
                    },
                    {
                        "value": 32,
                        "name": "晚点"
                    }
                ],
                "total": 6
            }
        """
        # 发起POST请求，op参数通过URL传递，token放在Header中的access_token字段
        return self.make_stock_authenticated_request('POST', '?op=stock_time')

    def get_stock_list(self) -> Dict[str, Any]:
        """
        获取库存列表

        接口URL：https://st.pcylsoft.com:9006/st/steelyard/?op=stocks
        Content-Type：application/x-www-form-urlencoded
        请求方式：post

        Returns:
            库存列表响应，包含以下字段：
            - code: 响应状态码 (200表示成功)
            - msg: 响应消息 ("success"表示成功)
            - data: 库存列表，每个商品包含：
                - code: 商品代码 (如"13010405")
                - name: 商品名称 (如"小白菜")
                - unit: 单位 (如"市斤")
                - brand: 品牌 (可能为null)
                - spec: 规格 (可能为null)
                - quantity: 库存数量 (如33.3)
            - total: 商品总数

        Example:
            >>> api = StockAPI("https://st.pcylsoft.com:9006/st/steelyard/")
            >>> api.set_access_token("your_token")
            >>> result = api.get_stock_list()
            >>> print(result)
            {
                "code": 200,
                "msg": "success",
                "data": [
                    {
                        "code": "13010405",
                        "name": "小白菜",
                        "unit": "市斤",
                        "brand": null,
                        "spec": null,
                        "quantity": 33.3
                    },
                    {
                        "code": "13010406",
                        "name": "西红柿",
                        "unit": "市斤",
                        "brand": null,
                        "spec": null,
                        "quantity": 3.8
                    }
                ],
                "total": 30
            }
        """
        # 发起POST请求，op参数通过URL传递，token放在Header中的access_token字段
        return self.make_stock_authenticated_request('POST', '?op=stocks')

    def submit_stock_out(self, datetime: str, depot_code: str, project_code: str,
                        time_type: int, people: int, details: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        提交出库申请

        接口URL：https://st.pcylsoft.com:9006/st/steelyard/?op=stock_submit
        Content-Type：application/json
        请求方式：post

        Args:
            datetime: 出库日期，格式YYYY-MM-DD (如"2024-12-20")
            depot_code: 仓库代码 (如"S01"，来自仓库列表的code字段)
            project_code: 项目代码 (如"P0001"，来自项目列表的code字段)
            time_type: 餐别值 (如1，来自餐别列表的value字段)
            people: 人数 (如100)
            details: 出库明细列表，每个明细包含：
                - code: 商品代码 (如"13010405")
                - quantity: 出库数量 (如"7.2"，通过串口获取的重量)
                - unit: 单位 (固定为"市斤")
                - path: 图片地址 (如"23/638702976856623258.jpg;"，多张图片以分号;隔开)

        Returns:
            出库提交响应，包含以下字段：
            - code: 响应状态码 (200表示成功)
            - msg: 响应消息 (成功时返回出库单号，如"YO25073117941")
            - total: 总数 (通常为0)

        Example:
            >>> api = StockAPI("https://st.pcylsoft.com:9006/st/steelyard/")
            >>> api.set_access_token("your_token")
            >>> details = [
            ...     {
            ...         "code": "13010405",
            ...         "quantity": "7.2",
            ...         "unit": "市斤",
            ...         "path": "23/638702976856623258.jpg;"
            ...     }
            ... ]
            >>> result = api.submit_stock_out(
            ...     datetime="2024-12-20",
            ...     depot_code="S01",
            ...     project_code="P0001",
            ...     time_type=1,
            ...     people=100,
            ...     details=details
            ... )
            >>> print(result)
            {
                "code": 200,
                "msg": "YO25073117941",
                "total": 0
            }
        """
        # 构建请求数据
        request_data = {
            "data": {
                "datetime": datetime,
                "depotcode": depot_code,
                "projectcode": project_code,
                "timetype": time_type,
                "people": people,
                "details": details
            }
        }

        # 准备请求参数
        data = {
            'op': 'stock_submit'
        }

        # 发起POST请求，使用JSON格式
        # 需要特殊处理，因为这个接口需要JSON格式的请求体
        return self._make_json_request('POST', '', params=data, json_data=request_data)

    def _make_json_request(self, method: str, endpoint: str, params: Dict[str, Any] = None,
                          json_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        发起需要认证的JSON请求

        Args:
            method: HTTP方法
            endpoint: 端点
            params: URL参数
            json_data: JSON请求体数据

        Returns:
            请求结果字典
        """
        if not self.is_authenticated():
            return {
                'code': 401,
                'msg': '未认证，请先登录',
                'data': None
            }

        url = f"{self.base_url}/{endpoint.lstrip('/')}" if endpoint else self.base_url

        try:
            # 设置请求头
            headers = {
                'access_token': self.access_token,
                'Content-Type': 'application/json'
            }

            response = self.session.request(
                method,
                url,
                params=params,
                json=json_data,
                headers=headers,
                timeout=10
            )

            # 检查是否是认证失败
            if response.status_code == 401:
                return {
                    'code': 401,
                    'msg': 'token已过期，请重新登录',
                    'data': None
                }

            response.raise_for_status()

            return response.json()

        except requests.exceptions.RequestException as e:
            return {
                'code': 500,
                'msg': f'网络请求失败: {str(e)}',
                'data': None
            }
        except Exception as e:
            return {
                'code': 500,
                'msg': f'请求过程中发生错误: {str(e)}',
                'data': None
            }

    def create_stock_detail(self, code: str, quantity: str, unit: str = "市斤",
                           path: str = "") -> Dict[str, Any]:
        """
        创建出库明细数据

        Args:
            code: 商品代码
            quantity: 出库数量（重量）
            unit: 单位，默认为"市斤"
            path: 图片地址，多张图片以分号;隔开

        Returns:
            出库明细字典
        """
        return {
            "code": code,
            "quantity": quantity,
            "unit": unit,
            "path": path
        }

    def validate_stock_out_data(self, datetime: str, depot_code: str, project_code: str,
                               time_type: int, people: int, details: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        验证出库数据的有效性

        Args:
            datetime: 出库日期
            depot_code: 仓库代码
            project_code: 项目代码
            time_type: 餐别值
            people: 人数
            details: 出库明细列表

        Returns:
            验证结果字典，包含：
            - valid: 是否有效 (bool)
            - errors: 错误列表 (list)
        """
        errors = []

        # 验证日期格式
        if not datetime or not isinstance(datetime, str):
            errors.append("出库日期不能为空")
        else:
            try:
                from datetime import datetime as dt
                dt.strptime(datetime, "%Y-%m-%d")
            except ValueError:
                errors.append("出库日期格式错误，应为YYYY-MM-DD")

        # 验证仓库代码
        if not depot_code or not isinstance(depot_code, str):
            errors.append("仓库代码不能为空")

        # 验证项目代码
        if not project_code or not isinstance(project_code, str):
            errors.append("项目代码不能为空")

        # 验证餐别值
        if not isinstance(time_type, int) or time_type not in [1, 2, 4, 8, 16, 32]:
            errors.append("餐别值必须是1, 2, 4, 8, 16, 32中的一个")

        # 验证人数
        if not isinstance(people, int) or people <= 0:
            errors.append("人数必须是正整数")

        # 验证明细列表
        if not details or not isinstance(details, list):
            errors.append("出库明细列表不能为空")
        else:
            for i, detail in enumerate(details):
                if not isinstance(detail, dict):
                    errors.append(f"明细{i+1}格式错误")
                    continue

                # 验证商品代码
                if not detail.get('code'):
                    errors.append(f"明细{i+1}商品代码不能为空")

                # 验证数量
                quantity = detail.get('quantity')
                if not quantity:
                    errors.append(f"明细{i+1}数量不能为空")
                else:
                    try:
                        float(quantity)
                    except (ValueError, TypeError):
                        errors.append(f"明细{i+1}数量格式错误")

                # 验证单位
                if not detail.get('unit'):
                    errors.append(f"明细{i+1}单位不能为空")

        return {
            'valid': len(errors) == 0,
            'errors': errors
        }

    def get_meal_time_name(self, time_type: int) -> str:
        """
        根据餐别值获取餐别名称

        Args:
            time_type: 餐别值

        Returns:
            餐别名称
        """
        meal_map = {
            1: "早餐",
            2: "早点",
            4: "午餐",
            8: "午点",
            16: "晚餐",
            32: "晚点"
        }
        return meal_map.get(time_type, f"未知餐别({time_type})")
