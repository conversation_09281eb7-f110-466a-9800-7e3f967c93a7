#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
串口管理器 - 统一管理串口资源，避免冲突
Serial Manager - Unified serial port resource management to avoid conflicts
"""

from PyQt6.QtCore import QObject, pyqtSignal
from typing import Optional, List, Callable


class SerialManager(QObject):
    """串口管理器 - 单例模式管理串口资源"""

    # 信号
    weight_received = pyqtSignal(str)  # 重量数据接收
    connection_status_changed = pyqtSignal(bool)  # 连接状态变化
    error_occurred = pyqtSignal(str)  # 错误发生
    port_detected = pyqtSignal(str)  # 端口检测

    _instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if SerialManager._initialized:
            return

        super().__init__()

        self.serial_worker = None
        self.is_connected = False
        self.current_port = None
        self.subscribers = []  # 订阅者列表
        SerialManager._initialized = True
    
    def add_subscriber(self, subscriber_id: str, callback_dict: dict):
        """
        添加订阅者
        
        Args:
            subscriber_id: 订阅者ID（如 'order_module', 'stock_out_module'）
            callback_dict: 回调函数字典，包含：
                - weight_received: 重量接收回调
                - connection_status: 连接状态回调
                - error_occurred: 错误回调
                - port_detected: 端口检测回调
        """
        # 移除已存在的订阅者
        self.remove_subscriber(subscriber_id)
        
        # 添加新订阅者
        subscriber = {
            'id': subscriber_id,
            'callbacks': callback_dict
        }
        self.subscribers.append(subscriber)
        
        # 如果已连接，立即通知新订阅者
        if self.is_connected:
            if 'connection_status' in callback_dict:
                callback_dict['connection_status'](True)
    
    def remove_subscriber(self, subscriber_id: str):
        """移除订阅者"""
        self.subscribers = [s for s in self.subscribers if s['id'] != subscriber_id]
    
    def start_serial_connection(self, force_restart=False):
        """启动串口连接"""
        if self.is_connected and not force_restart:
            # 已连接，通知所有订阅者
            self._notify_connection_status(True)
            return True
        
        try:
            # 导入串口工作类
            from ui.modules.weight_submission_module import SerialWorker
            from config.settings import settings
            
            # 停止现有连接
            if self.serial_worker:
                self.serial_worker.stop()
                self.serial_worker = None
            
            # 获取串口配置
            port = settings.serial_port
            baudrate = settings.serial_baudrate
            timeout = settings.serial_timeout
            auto_detect = settings.serial_auto_detect
            
            # 创建串口工作线程
            self.serial_worker = SerialWorker(port, baudrate, timeout, auto_detect)
            
            # 连接信号
            self.serial_worker.weight_received.connect(self._on_weight_received)
            self.serial_worker.error_occurred.connect(self._on_error_occurred)
            self.serial_worker.connection_status.connect(self._on_connection_status_changed)
            self.serial_worker.port_detected.connect(self._on_port_detected)
            
            # 启动工作线程
            self.serial_worker.start()
            
            return True
            
        except Exception as e:
            self._notify_error(f"启动串口连接失败: {str(e)}")
            return False
    
    def stop_serial_connection(self):
        """停止串口连接"""
        if self.serial_worker:
            self.serial_worker.stop()
            self.serial_worker = None
        
        self.is_connected = False
        self.current_port = None
        self._notify_connection_status(False)
    
    def get_connection_status(self):
        """获取连接状态"""
        return {
            'connected': self.is_connected,
            'port': self.current_port,
            'subscriber_count': len(self.subscribers)
        }
    
    def _on_weight_received(self, weight: str):
        """重量数据接收处理"""
        self._notify_weight_received(weight)
    
    def _on_error_occurred(self, error: str):
        """错误处理"""
        self._notify_error(error)
    
    def _on_connection_status_changed(self, connected: bool):
        """连接状态变化处理"""
        self.is_connected = connected
        if connected and self.serial_worker:
            self.current_port = self.serial_worker.port
        else:
            self.current_port = None
        
        self._notify_connection_status(connected)
    
    def _on_port_detected(self, port: str):
        """端口检测处理"""
        self.current_port = port
        self._notify_port_detected(port)
    
    def _notify_weight_received(self, weight: str):
        """通知所有订阅者重量数据"""
        for subscriber in self.subscribers:
            callback = subscriber['callbacks'].get('weight_received')
            if callback:
                try:
                    callback(weight)
                except Exception as e:
                    print(f"订阅者 {subscriber['id']} 重量回调错误: {e}")
    
    def _notify_connection_status(self, connected: bool):
        """通知所有订阅者连接状态"""
        for subscriber in self.subscribers:
            callback = subscriber['callbacks'].get('connection_status')
            if callback:
                try:
                    callback(connected)
                except Exception as e:
                    print(f"订阅者 {subscriber['id']} 连接状态回调错误: {e}")
    
    def _notify_error(self, error: str):
        """通知所有订阅者错误信息"""
        for subscriber in self.subscribers:
            callback = subscriber['callbacks'].get('error_occurred')
            if callback:
                try:
                    callback(error)
                except Exception as e:
                    print(f"订阅者 {subscriber['id']} 错误回调错误: {e}")
    
    def _notify_port_detected(self, port: str):
        """通知所有订阅者端口检测"""
        for subscriber in self.subscribers:
            callback = subscriber['callbacks'].get('port_detected')
            if callback:
                try:
                    callback(port)
                except Exception as e:
                    print(f"订阅者 {subscriber['id']} 端口检测回调错误: {e}")


# 全局串口管理器实例
serial_manager = SerialManager()
