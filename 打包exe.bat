@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 智慧食堂管理系统 - 自动打包脚本
echo ========================================
echo.

echo 🚀 开始自动打包...
echo.

echo 📋 检查Python环境...
python --version
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo 请先安装Python 3.8+并添加到系统PATH
    pause
    exit /b 1
)

echo.
echo 📦 运行打包脚本...
python build_exe.py

if errorlevel 1 (
    echo.
    echo ❌ 打包失败，请查看上方错误信息
    pause
    exit /b 1
)

echo.
echo ✅ 打包完成！
echo.
echo 📁 输出文件位于 release 目录:
echo    - 智慧食堂管理系统.exe (单文件版本)
echo    - 智慧食堂管理系统_目录版 (目录版本)
echo    - config (配置文件)
echo    - 启动程序.bat (启动脚本)
echo    - 使用说明.txt (使用说明)
echo.
echo 🚀 可以直接运行 release\智慧食堂管理系统.exe
echo.

set /p choice="是否打开release目录? (y/n): "
if /i "%choice%"=="y" (
    explorer release
)

pause
