# -*- mode: python ; coding: utf-8 -*-

import os
import sys
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# 获取项目根目录
project_root = os.path.dirname(os.path.abspath(SPEC))

# 收集数据文件
datas = []

# 添加配置文件目录
config_dir = os.path.join(project_root, 'config')
if os.path.exists(config_dir):
    datas.append((config_dir, 'config'))

# 添加API模块
api_dir = os.path.join(project_root, 'api')
if os.path.exists(api_dir):
    datas.append((api_dir, 'api'))

# 添加UI模块
ui_dir = os.path.join(project_root, 'ui')
if os.path.exists(ui_dir):
    datas.append((ui_dir, 'ui'))

# 添加utils模块
utils_dir = os.path.join(project_root, 'utils')
if os.path.exists(utils_dir):
    datas.append((utils_dir, 'utils'))

# 创建photos目录（如果不存在）
photos_dir = os.path.join(project_root, 'photos')
os.makedirs(photos_dir, exist_ok=True)
datas.append((photos_dir, 'photos'))

# 创建captured_images目录（如果不存在）
captured_dir = os.path.join(project_root, 'captured_images')
os.makedirs(captured_dir, exist_ok=True)
datas.append((captured_dir, 'captured_images'))

# 收集隐藏导入
hiddenimports = [
    'PyQt6.QtCore',
    'PyQt6.QtGui',
    'PyQt6.QtWidgets',
    'requests',
    'json',
    'os',
    'sys',
    'traceback',
    'datetime',
    'threading',
    'urllib.parse',
    'base64',
    'hashlib',
    'time',
    # 摄像头相关
    'cv2',
    'numpy',
    # 串口相关
    'serial',
    'serial.tools',
    'serial.tools.list_ports',
    # 其他必要模块
    're',
    'typing',
    'functools',
    'collections',
    'itertools',
    'pathlib',
]

# 收集PyQt6相关模块
hiddenimports.extend([
    'PyQt6.sip',
    'PyQt6.QtNetwork',
    'PyQt6.QtPrintSupport',
    'PyQt6.QtMultimedia',
    'PyQt6.QtMultimediaWidgets',
])

# 分析主程序
a = Analysis(
    ['main.py'],
    pathex=[project_root],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'scipy',
        'pandas',
        'PIL',
        'tensorflow',
        'torch',
        'jupyter',
        'notebook',
        'IPython',
        'sphinx',
        'pytest',
        'setuptools',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 创建PYZ文件
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# 创建可执行文件
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='智慧食堂管理系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 设置为False以隐藏控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    # icon='icon.ico',  # 如果有图标文件可以取消注释
)

# 创建分发目录（可选，用于创建目录版本）
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='智慧食堂管理系统_目录版',
)
