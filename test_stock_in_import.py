#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试入库模块导入
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("正在测试入库模块导入...")
    
    # 测试API导入
    print("1. 测试StockInAPI导入...")
    from api.stock_in_api import StockInAPI
    print("✅ StockInAPI导入成功")
    
    # 测试样式导入
    print("2. 测试样式导入...")
    from ui.styles import GlassmorphismStyles
    styles = GlassmorphismStyles()
    stock_in_styles = styles.get_stock_in_styles()
    print("✅ 样式导入成功")
    
    # 测试模块导入
    print("3. 测试StockInModule导入...")
    from ui.modules.stock_in_module import StockInModule
    print("✅ StockInModule导入成功")
    
    print("\n🎉 所有导入测试通过！")
    
except Exception as e:
    print(f"❌ 导入失败: {e}")
    import traceback
    traceback.print_exc()
