(['C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\main.py'],
 ['C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui',
  'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui'],
 ['PyQt6.QtCore',
  'PyQt6.QtGui',
  'PyQt6.QtWidgets',
  'requests',
  'json',
  'os',
  'sys',
  'traceback',
  'datetime',
  'threading',
  'urllib.parse',
  'base64',
  'hashlib',
  'time',
  'cv2',
  'numpy',
  'serial',
  'serial.tools',
  'serial.tools.list_ports',
  're',
  'typing',
  'functools',
  'collections',
  'itertools',
  'pathlib',
  'PyQt6.sip',
  'PyQt6.QtNetwork',
  'PyQt6.QtPrintSupport',
  'PyQt6.QtMultimedia',
  'PyQt6.QtMultimediaWidgets'],
 [('D:\\python\\Lib\\site-packages\\numpy\\_pyinstaller', 0),
  ('D:\\python\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('D:\\python\\Lib\\site-packages\\_pyinstaller_hooks_contrib', -1000)],
 {},
 ['tkinter',
  'matplotlib',
  'scipy',
  'pandas',
  'PIL',
  'tensorflow',
  'torch',
  'jupyter',
  'notebook',
  'IPython',
  'sphinx',
  'pytest',
  'setuptools',
  '__main__'],
 [],
 False,
 {},
 0,
 [],
 [('api\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\__init__.py',
   'DATA'),
  ('api\\__pycache__\\__init__.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('api\\__pycache__\\auth_api.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\__pycache__\\auth_api.cpython-313.pyc',
   'DATA'),
  ('api\\__pycache__\\canteen_api.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\__pycache__\\canteen_api.cpython-313.pyc',
   'DATA'),
  ('api\\__pycache__\\order_api.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\__pycache__\\order_api.cpython-313.pyc',
   'DATA'),
  ('api\\__pycache__\\stock_api.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\__pycache__\\stock_api.cpython-313.pyc',
   'DATA'),
  ('api\\__pycache__\\stock_in_api.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\__pycache__\\stock_in_api.cpython-313.pyc',
   'DATA'),
  ('api\\__pycache__\\weight_api.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\__pycache__\\weight_api.cpython-313.pyc',
   'DATA'),
  ('api\\auth_api.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\auth_api.py',
   'DATA'),
  ('api\\canteen_api.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\canteen_api.py',
   'DATA'),
  ('api\\order_api.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\order_api.py',
   'DATA'),
  ('api\\stock_api.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\stock_api.py',
   'DATA'),
  ('api\\stock_in_api.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\stock_in_api.py',
   'DATA'),
  ('api\\weight_api.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\weight_api.py',
   'DATA'),
  ('config\\GrilmorphismUI.json',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\config\\GrilmorphismUI.json',
   'DATA'),
  ('config\\__pycache__\\settings.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\config\\__pycache__\\settings.cpython-313.pyc',
   'DATA'),
  ('config\\app_config.json',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\config\\app_config.json',
   'DATA'),
  ('config\\auth_storage.json',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\config\\auth_storage.json',
   'DATA'),
  ('config\\settings.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\config\\settings.py',
   'DATA'),
  ('photos\\auto_photo_20250730_183544.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\auto_photo_20250730_183544.jpg',
   'DATA'),
  ('photos\\auto_photo_20250731_144007.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\auto_photo_20250731_144007.jpg',
   'DATA'),
  ('photos\\photo_20250730_183015.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250730_183015.jpg',
   'DATA'),
  ('photos\\photo_20250730_184018.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250730_184018.jpg',
   'DATA'),
  ('photos\\photo_20250730_184020.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250730_184020.jpg',
   'DATA'),
  ('photos\\photo_20250730_184215.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250730_184215.jpg',
   'DATA'),
  ('photos\\photo_20250731_081840.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250731_081840.jpg',
   'DATA'),
  ('photos\\photo_20250731_082920.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250731_082920.jpg',
   'DATA'),
  ('photos\\photo_20250731_085629.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250731_085629.jpg',
   'DATA'),
  ('photos\\photo_20250731_101724.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250731_101724.jpg',
   'DATA'),
  ('photos\\photo_20250731_101726.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250731_101726.jpg',
   'DATA'),
  ('photos\\photo_20250731_101728.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250731_101728.jpg',
   'DATA'),
  ('photos\\photo_20250731_111513.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250731_111513.jpg',
   'DATA'),
  ('photos\\photo_20250731_144446.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250731_144446.jpg',
   'DATA'),
  ('photos\\photo_20250731_153313.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250731_153313.jpg',
   'DATA'),
  ('photos\\photo_20250731_170444.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250731_170444.jpg',
   'DATA'),
  ('photos\\photo_20250801_194325.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_194325.jpg',
   'DATA'),
  ('photos\\photo_20250801_222155.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_222155.jpg',
   'DATA'),
  ('photos\\photo_20250801_222201.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_222201.jpg',
   'DATA'),
  ('photos\\photo_20250801_222205.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_222205.jpg',
   'DATA'),
  ('photos\\photo_20250801_222220.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_222220.jpg',
   'DATA'),
  ('photos\\photo_20250801_222440.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_222440.jpg',
   'DATA'),
  ('photos\\photo_20250801_222456.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_222456.jpg',
   'DATA'),
  ('photos\\photo_20250801_222505.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_222505.jpg',
   'DATA'),
  ('photos\\photo_20250801_222510.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_222510.jpg',
   'DATA'),
  ('photos\\photo_20250801_222518.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_222518.jpg',
   'DATA'),
  ('photos\\photo_20250801_223922.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_223922.jpg',
   'DATA'),
  ('photos\\photo_20250801_225011.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_225011.jpg',
   'DATA'),
  ('ui\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\__init__.py',
   'DATA'),
  ('ui\\__pycache__\\__init__.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('ui\\__pycache__\\global_serial_manager.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\__pycache__\\global_serial_manager.cpython-313.pyc',
   'DATA'),
  ('ui\\__pycache__\\login_window.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\__pycache__\\login_window.cpython-313.pyc',
   'DATA'),
  ('ui\\__pycache__\\main_window.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\__pycache__\\main_window.cpython-313.pyc',
   'DATA'),
  ('ui\\__pycache__\\serial_manager.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\__pycache__\\serial_manager.cpython-313.pyc',
   'DATA'),
  ('ui\\__pycache__\\styles.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\__pycache__\\styles.cpython-313.pyc',
   'DATA'),
  ('ui\\dialogs\\__pycache__\\serial_config_dialog.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\dialogs\\__pycache__\\serial_config_dialog.cpython-313.pyc',
   'DATA'),
  ('ui\\dialogs\\serial_config_dialog.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\dialogs\\serial_config_dialog.py',
   'DATA'),
  ('ui\\global_serial_manager.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\global_serial_manager.py',
   'DATA'),
  ('ui\\login_window.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\login_window.py',
   'DATA'),
  ('ui\\main_window.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\main_window.py',
   'DATA'),
  ('ui\\modules\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\__init__.py',
   'DATA'),
  ('ui\\modules\\__pycache__\\__init__.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('ui\\modules\\__pycache__\\camera_module.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\__pycache__\\camera_module.cpython-313.pyc',
   'DATA'),
  ('ui\\modules\\__pycache__\\order_module.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\__pycache__\\order_module.cpython-313.pyc',
   'DATA'),
  ('ui\\modules\\__pycache__\\recipe_module.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\__pycache__\\recipe_module.cpython-313.pyc',
   'DATA'),
  ('ui\\modules\\__pycache__\\stock_in_module.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\__pycache__\\stock_in_module.cpython-313.pyc',
   'DATA'),
  ('ui\\modules\\__pycache__\\stock_out_module.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\__pycache__\\stock_out_module.cpython-313.pyc',
   'DATA'),
  ('ui\\modules\\__pycache__\\weight_submission_module.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\__pycache__\\weight_submission_module.cpython-313.pyc',
   'DATA'),
  ('ui\\modules\\camera_module.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\camera_module.py',
   'DATA'),
  ('ui\\modules\\order_module.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\order_module.py',
   'DATA'),
  ('ui\\modules\\recipe_module.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\recipe_module.py',
   'DATA'),
  ('ui\\modules\\stock_in_module.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\stock_in_module.py',
   'DATA'),
  ('ui\\modules\\stock_out_module.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\stock_out_module.py',
   'DATA'),
  ('ui\\modules\\weight_submission_module.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\weight_submission_module.py',
   'DATA'),
  ('ui\\serial_manager.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\serial_manager.py',
   'DATA'),
  ('ui\\styles.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\styles.py',
   'DATA'),
  ('utils\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\utils\\__init__.py',
   'DATA'),
  ('utils\\__pycache__\\__init__.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\utils\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('utils\\__pycache__\\auth_manager.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\utils\\__pycache__\\auth_manager.cpython-313.pyc',
   'DATA'),
  ('utils\\auth_manager.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\utils\\auth_manager.py',
   'DATA')],
 '3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit '
 '(AMD64)]',
 [('pyi_rth_pyqt6',
   'D:\\python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt6.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('main',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\main.py',
   'PYSOURCE')],
 [('subprocess', 'D:\\python\\Lib\\subprocess.py', 'PYMODULE'),
  ('selectors', 'D:\\python\\Lib\\selectors.py', 'PYMODULE'),
  ('contextlib', 'D:\\python\\Lib\\contextlib.py', 'PYMODULE'),
  ('signal', 'D:\\python\\Lib\\signal.py', 'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\python\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\python\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\python\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\python\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\python\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\python\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client', 'D:\\python\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('xmlrpc', 'D:\\python\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('gzip', 'D:\\python\\Lib\\gzip.py', 'PYMODULE'),
  ('argparse', 'D:\\python\\Lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'D:\\python\\Lib\\textwrap.py', 'PYMODULE'),
  ('copy', 'D:\\python\\Lib\\copy.py', 'PYMODULE'),
  ('gettext', 'D:\\python\\Lib\\gettext.py', 'PYMODULE'),
  ('_compression', 'D:\\python\\Lib\\_compression.py', 'PYMODULE'),
  ('xml.parsers.expat', 'D:\\python\\Lib\\xml\\parsers\\expat.py', 'PYMODULE'),
  ('xml.parsers', 'D:\\python\\Lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml', 'D:\\python\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\python\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils', 'D:\\python\\Lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('urllib.request', 'D:\\python\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('urllib', 'D:\\python\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('ipaddress', 'D:\\python\\Lib\\ipaddress.py', 'PYMODULE'),
  ('fnmatch', 'D:\\python\\Lib\\fnmatch.py', 'PYMODULE'),
  ('getpass', 'D:\\python\\Lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'D:\\python\\Lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'D:\\python\\Lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'D:\\python\\Lib\\netrc.py', 'PYMODULE'),
  ('mimetypes', 'D:\\python\\Lib\\mimetypes.py', 'PYMODULE'),
  ('getopt', 'D:\\python\\Lib\\getopt.py', 'PYMODULE'),
  ('email.utils', 'D:\\python\\Lib\\email\\utils.py', 'PYMODULE'),
  ('random', 'D:\\python\\Lib\\random.py', 'PYMODULE'),
  ('statistics', 'D:\\python\\Lib\\statistics.py', 'PYMODULE'),
  ('fractions', 'D:\\python\\Lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'D:\\python\\Lib\\numbers.py', 'PYMODULE'),
  ('email.charset', 'D:\\python\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders', 'D:\\python\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('quopri', 'D:\\python\\Lib\\quopri.py', 'PYMODULE'),
  ('email.errors', 'D:\\python\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.quoprimime', 'D:\\python\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.base64mime', 'D:\\python\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email._parseaddr', 'D:\\python\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('calendar', 'D:\\python\\Lib\\calendar.py', 'PYMODULE'),
  ('http.cookiejar', 'D:\\python\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http', 'D:\\python\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('ssl', 'D:\\python\\Lib\\ssl.py', 'PYMODULE'),
  ('urllib.response', 'D:\\python\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('urllib.error', 'D:\\python\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('string', 'D:\\python\\Lib\\string.py', 'PYMODULE'),
  ('email', 'D:\\python\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser', 'D:\\python\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email._policybase', 'D:\\python\\Lib\\email\\_policybase.py', 'PYMODULE'),
  ('email.feedparser', 'D:\\python\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.message', 'D:\\python\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.policy', 'D:\\python\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\python\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\python\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'D:\\python\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.generator', 'D:\\python\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\python\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\python\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.header', 'D:\\python\\Lib\\email\\header.py', 'PYMODULE'),
  ('bisect', 'D:\\python\\Lib\\bisect.py', 'PYMODULE'),
  ('xml.sax', 'D:\\python\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax.handler', 'D:\\python\\Lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\python\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader', 'D:\\python\\Lib\\xml\\sax\\xmlreader.py', 'PYMODULE'),
  ('http.client', 'D:\\python\\Lib\\http\\client.py', 'PYMODULE'),
  ('decimal', 'D:\\python\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\python\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'D:\\python\\Lib\\contextvars.py', 'PYMODULE'),
  ('hmac', 'D:\\python\\Lib\\hmac.py', 'PYMODULE'),
  ('struct', 'D:\\python\\Lib\\struct.py', 'PYMODULE'),
  ('socket', 'D:\\python\\Lib\\socket.py', 'PYMODULE'),
  ('tempfile', 'D:\\python\\Lib\\tempfile.py', 'PYMODULE'),
  ('shutil', 'D:\\python\\Lib\\shutil.py', 'PYMODULE'),
  ('zipfile', 'D:\\python\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path', 'D:\\python\\Lib\\zipfile\\_path\\__init__.py', 'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\python\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('py_compile', 'D:\\python\\Lib\\py_compile.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\python\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib', 'D:\\python\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\python\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\python\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\python\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('csv', 'D:\\python\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\python\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\python\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.abc', 'D:\\python\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\python\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\python\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'D:\\python\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\python\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\python\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib._abc', 'D:\\python\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\python\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\python\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\python\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\python\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('inspect', 'D:\\python\\Lib\\inspect.py', 'PYMODULE'),
  ('token', 'D:\\python\\Lib\\token.py', 'PYMODULE'),
  ('dis', 'D:\\python\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\python\\Lib\\opcode.py', 'PYMODULE'),
  ('_opcode_metadata', 'D:\\python\\Lib\\_opcode_metadata.py', 'PYMODULE'),
  ('ast', 'D:\\python\\Lib\\ast.py', 'PYMODULE'),
  ('__future__', 'D:\\python\\Lib\\__future__.py', 'PYMODULE'),
  ('importlib.readers', 'D:\\python\\Lib\\importlib\\readers.py', 'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\python\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\python\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('tokenize', 'D:\\python\\Lib\\tokenize.py', 'PYMODULE'),
  ('importlib.util', 'D:\\python\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('tarfile', 'D:\\python\\Lib\\tarfile.py', 'PYMODULE'),
  ('lzma', 'D:\\python\\Lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'D:\\python\\Lib\\bz2.py', 'PYMODULE'),
  ('logging', 'D:\\python\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('pickle', 'D:\\python\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\python\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses', 'D:\\python\\Lib\\dataclasses.py', 'PYMODULE'),
  ('_compat_pickle', 'D:\\python\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('multiprocessing.context',
   'D:\\python\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\python\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\python\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\python\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\python\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\python\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\python\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes', 'D:\\python\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes.util', 'D:\\python\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes._aix', 'D:\\python\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\python\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\python\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\python\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\python\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes._endian', 'D:\\python\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\python\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\python\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\python\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue', 'D:\\python\\Lib\\queue.py', 'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\python\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\python\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\python\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\python\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'D:\\python\\Lib\\secrets.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\python\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\python\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy', 'D:\\python\\Lib\\runpy.py', 'PYMODULE'),
  ('pkgutil', 'D:\\python\\Lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'D:\\python\\Lib\\zipimport.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\python\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'D:\\python\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\python\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('PyQt6', 'D:\\python\\Lib\\site-packages\\PyQt6\\__init__.py', 'PYMODULE'),
  ('pathlib', 'D:\\python\\Lib\\pathlib\\__init__.py', 'PYMODULE'),
  ('pathlib._local', 'D:\\python\\Lib\\pathlib\\_local.py', 'PYMODULE'),
  ('glob', 'D:\\python\\Lib\\glob.py', 'PYMODULE'),
  ('pathlib._abc', 'D:\\python\\Lib\\pathlib\\_abc.py', 'PYMODULE'),
  ('typing', 'D:\\python\\Lib\\typing.py', 'PYMODULE'),
  ('serial.tools.list_ports',
   'D:\\python\\Lib\\site-packages\\serial\\tools\\list_ports.py',
   'PYMODULE'),
  ('serial.tools.list_ports_posix',
   'D:\\python\\Lib\\site-packages\\serial\\tools\\list_ports_posix.py',
   'PYMODULE'),
  ('serial.tools.list_ports_osx',
   'D:\\python\\Lib\\site-packages\\serial\\tools\\list_ports_osx.py',
   'PYMODULE'),
  ('serial.tools.list_ports_linux',
   'D:\\python\\Lib\\site-packages\\serial\\tools\\list_ports_linux.py',
   'PYMODULE'),
  ('serial.tools.list_ports_common',
   'D:\\python\\Lib\\site-packages\\serial\\tools\\list_ports_common.py',
   'PYMODULE'),
  ('serial.tools.list_ports_windows',
   'D:\\python\\Lib\\site-packages\\serial\\tools\\list_ports_windows.py',
   'PYMODULE'),
  ('serial.win32',
   'D:\\python\\Lib\\site-packages\\serial\\win32.py',
   'PYMODULE'),
  ('ctypes.wintypes', 'D:\\python\\Lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('serial.tools',
   'D:\\python\\Lib\\site-packages\\serial\\tools\\__init__.py',
   'PYMODULE'),
  ('serial', 'D:\\python\\Lib\\site-packages\\serial\\__init__.py', 'PYMODULE'),
  ('serial.serialjava',
   'D:\\python\\Lib\\site-packages\\serial\\serialjava.py',
   'PYMODULE'),
  ('serial.serialposix',
   'D:\\python\\Lib\\site-packages\\serial\\serialposix.py',
   'PYMODULE'),
  ('serial.serialwin32',
   'D:\\python\\Lib\\site-packages\\serial\\serialwin32.py',
   'PYMODULE'),
  ('serial.serialcli',
   'D:\\python\\Lib\\site-packages\\serial\\serialcli.py',
   'PYMODULE'),
  ('serial.serialutil',
   'D:\\python\\Lib\\site-packages\\serial\\serialutil.py',
   'PYMODULE'),
  ('numpy', 'D:\\python\\Lib\\site-packages\\numpy\\__init__.py', 'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'D:\\python\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\python\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.strings',
   'D:\\python\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'D:\\python\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'D:\\python\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'D:\\python\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'D:\\python\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy._utils',
   'D:\\python\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'D:\\python\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'D:\\python\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\python\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'D:\\python\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.char',
   'D:\\python\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'D:\\python\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'D:\\python\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'D:\\python\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'D:\\python\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'D:\\python\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'D:\\python\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'D:\\python\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'D:\\python\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'D:\\python\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'D:\\python\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'D:\\python\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'D:\\python\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'D:\\python\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.rec',
   'D:\\python\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy._core.records',
   'D:\\python\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\python\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\python\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\python\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\python\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\python\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'D:\\python\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing',
   'D:\\python\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'D:\\python\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\python\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\python\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\python\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\python\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy.f2py',
   'D:\\python\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'D:\\python\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'D:\\python\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'D:\\python\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'D:\\python\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'D:\\python\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'D:\\python\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'D:\\python\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'D:\\python\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'D:\\python\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'D:\\python\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'D:\\python\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'D:\\python\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'D:\\python\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'D:\\python\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\python\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\python\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\python\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\python\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\python\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\python\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\python\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\python\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('platform', 'D:\\python\\Lib\\platform.py', 'PYMODULE'),
  ('_ios_support', 'D:\\python\\Lib\\_ios_support.py', 'PYMODULE'),
  ('fileinput', 'D:\\python\\Lib\\fileinput.py', 'PYMODULE'),
  ('numpy.f2py.symbolic',
   'D:\\python\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'D:\\python\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'D:\\python\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'D:\\python\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'D:\\python\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.matlib',
   'D:\\python\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\python\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.testing',
   'D:\\python\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'D:\\python\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'D:\\python\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\python\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\python\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'D:\\python\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('sysconfig', 'D:\\python\\Lib\\sysconfig\\__init__.py', 'PYMODULE'),
  ('_aix_support', 'D:\\python\\Lib\\_aix_support.py', 'PYMODULE'),
  ('numpy.testing._private.utils',
   'D:\\python\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('doctest', 'D:\\python\\Lib\\doctest.py', 'PYMODULE'),
  ('_colorize', 'D:\\python\\Lib\\_colorize.py', 'PYMODULE'),
  ('pdb', 'D:\\python\\Lib\\pdb.py', 'PYMODULE'),
  ('pydoc', 'D:\\python\\Lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'D:\\python\\Lib\\webbrowser.py', 'PYMODULE'),
  ('http.server', 'D:\\python\\Lib\\http\\server.py', 'PYMODULE'),
  ('socketserver', 'D:\\python\\Lib\\socketserver.py', 'PYMODULE'),
  ('html', 'D:\\python\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'D:\\python\\Lib\\html\\entities.py', 'PYMODULE'),
  ('pydoc_data.topics', 'D:\\python\\Lib\\pydoc_data\\topics.py', 'PYMODULE'),
  ('pydoc_data', 'D:\\python\\Lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('_pyrepl.pager', 'D:\\python\\Lib\\_pyrepl\\pager.py', 'PYMODULE'),
  ('_pyrepl', 'D:\\python\\Lib\\_pyrepl\\__init__.py', 'PYMODULE'),
  ('tty', 'D:\\python\\Lib\\tty.py', 'PYMODULE'),
  ('shlex', 'D:\\python\\Lib\\shlex.py', 'PYMODULE'),
  ('rlcompleter', 'D:\\python\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('codeop', 'D:\\python\\Lib\\codeop.py', 'PYMODULE'),
  ('code', 'D:\\python\\Lib\\code.py', 'PYMODULE'),
  ('bdb', 'D:\\python\\Lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'D:\\python\\Lib\\cmd.py', 'PYMODULE'),
  ('difflib', 'D:\\python\\Lib\\difflib.py', 'PYMODULE'),
  ('numpy._core.tests._natype',
   'D:\\python\\Lib\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('concurrent.futures',
   'D:\\python\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\python\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\python\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\python\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent', 'D:\\python\\Lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('unittest.case', 'D:\\python\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest._log', 'D:\\python\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('unittest.util', 'D:\\python\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('unittest.result', 'D:\\python\\Lib\\unittest\\result.py', 'PYMODULE'),
  ('numpy.testing._private',
   'D:\\python\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('unittest', 'D:\\python\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest.async_case',
   'D:\\python\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('asyncio', 'D:\\python\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\python\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log', 'D:\\python\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\python\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\python\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\python\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\python\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\python\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads', 'D:\\python\\Lib\\asyncio\\threads.py', 'PYMODULE'),
  ('asyncio.taskgroups', 'D:\\python\\Lib\\asyncio\\taskgroups.py', 'PYMODULE'),
  ('asyncio.subprocess', 'D:\\python\\Lib\\asyncio\\subprocess.py', 'PYMODULE'),
  ('asyncio.streams', 'D:\\python\\Lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.runners', 'D:\\python\\Lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.base_events',
   'D:\\python\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.trsock', 'D:\\python\\Lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.staggered', 'D:\\python\\Lib\\asyncio\\staggered.py', 'PYMODULE'),
  ('asyncio.timeouts', 'D:\\python\\Lib\\asyncio\\timeouts.py', 'PYMODULE'),
  ('asyncio.tasks', 'D:\\python\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.queues', 'D:\\python\\Lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.base_tasks', 'D:\\python\\Lib\\asyncio\\base_tasks.py', 'PYMODULE'),
  ('asyncio.locks', 'D:\\python\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.mixins', 'D:\\python\\Lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.sslproto', 'D:\\python\\Lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.transports', 'D:\\python\\Lib\\asyncio\\transports.py', 'PYMODULE'),
  ('asyncio.protocols', 'D:\\python\\Lib\\asyncio\\protocols.py', 'PYMODULE'),
  ('asyncio.futures', 'D:\\python\\Lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\python\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions', 'D:\\python\\Lib\\asyncio\\exceptions.py', 'PYMODULE'),
  ('asyncio.events', 'D:\\python\\Lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\python\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.coroutines', 'D:\\python\\Lib\\asyncio\\coroutines.py', 'PYMODULE'),
  ('asyncio.constants', 'D:\\python\\Lib\\asyncio\\constants.py', 'PYMODULE'),
  ('unittest.signals', 'D:\\python\\Lib\\unittest\\signals.py', 'PYMODULE'),
  ('unittest.main', 'D:\\python\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.runner', 'D:\\python\\Lib\\unittest\\runner.py', 'PYMODULE'),
  ('unittest.loader', 'D:\\python\\Lib\\unittest\\loader.py', 'PYMODULE'),
  ('unittest.suite', 'D:\\python\\Lib\\unittest\\suite.py', 'PYMODULE'),
  ('numpy.exceptions',
   'D:\\python\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\python\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'D:\\python\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\python\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\python\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'D:\\python\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'D:\\python\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\python\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\python\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\python\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\python\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\python\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\python\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\python\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\python\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\python\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\python\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\python\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\python\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'D:\\python\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\python\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\python\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'D:\\python\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\python\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\python\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\python\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'D:\\python\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'D:\\python\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\python\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'D:\\python\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\python\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'D:\\python\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\python\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\python\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'D:\\python\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'D:\\python\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'D:\\python\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'D:\\python\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'D:\\python\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'D:\\python\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'D:\\python\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'D:\\python\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'D:\\python\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'D:\\python\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'D:\\python\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'D:\\python\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'D:\\python\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'D:\\python\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\python\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'D:\\python\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\python\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'D:\\python\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\python\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'D:\\python\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\python\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\python\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'D:\\python\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'D:\\python\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'D:\\python\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core',
   'D:\\python\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'D:\\python\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'D:\\python\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'D:\\python\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.__config__',
   'D:\\python\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\python\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\python\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'D:\\python\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\python\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'D:\\python\\Lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('_strptime', 'D:\\python\\Lib\\_strptime.py', 'PYMODULE'),
  ('hashlib', 'D:\\python\\Lib\\hashlib.py', 'PYMODULE'),
  ('base64', 'D:\\python\\Lib\\base64.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\python\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('threading', 'D:\\python\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local', 'D:\\python\\Lib\\_threading_local.py', 'PYMODULE'),
  ('datetime', 'D:\\python\\Lib\\datetime.py', 'PYMODULE'),
  ('_pydatetime', 'D:\\python\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('json', 'D:\\python\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder', 'D:\\python\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.decoder', 'D:\\python\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\python\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('requests',
   'D:\\python\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\python\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\python\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\python\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('http.cookies', 'D:\\python\\Lib\\http\\cookies.py', 'PYMODULE'),
  ('requests.models',
   'D:\\python\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('idna', 'D:\\python\\Lib\\site-packages\\idna\\__init__.py', 'PYMODULE'),
  ('idna.package_data',
   'D:\\python\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\python\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core', 'D:\\python\\Lib\\site-packages\\idna\\core.py', 'PYMODULE'),
  ('idna.uts46data',
   'D:\\python\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'D:\\python\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\python\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\python\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\python\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'D:\\python\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\python\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\python\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\python\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'D:\\python\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\python\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\python\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\python\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\python\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\python\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\python\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\python\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\python\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'D:\\python\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.http2',
   'D:\\python\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'D:\\python\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\python\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'D:\\python\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\python\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\python\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'D:\\python\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\python\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\python\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\python\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\python\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\python\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\python\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\python\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\python\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\python\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\python\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('requests.__version__',
   'D:\\python\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\python\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\python\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'D:\\python\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\python\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('requests.packages',
   'D:\\python\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\python\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\python\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\python\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\python\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('urllib3',
   'D:\\python\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'D:\\python\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'D:\\python\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'D:\\python\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'D:\\python\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'D:\\python\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\python\\Lib\\_py_abc.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\python\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('stringprep', 'D:\\python\\Lib\\stringprep.py', 'PYMODULE'),
  ('ui.main_window',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\main_window.py',
   'PYMODULE'),
  ('ui',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\__init__.py',
   'PYMODULE'),
  ('ui.dialogs.serial_config_dialog',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\dialogs\\serial_config_dialog.py',
   'PYMODULE'),
  ('ui.dialogs', '-', 'PYMODULE'),
  ('ui.global_serial_manager',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\global_serial_manager.py',
   'PYMODULE'),
  ('api.stock_in_api',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\stock_in_api.py',
   'PYMODULE'),
  ('api',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\__init__.py',
   'PYMODULE'),
  ('api.auth_api',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\auth_api.py',
   'PYMODULE'),
  ('api.stock_api',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\stock_api.py',
   'PYMODULE'),
  ('api.weight_api',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\weight_api.py',
   'PYMODULE'),
  ('api.order_api',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\order_api.py',
   'PYMODULE'),
  ('api.canteen_api',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\canteen_api.py',
   'PYMODULE'),
  ('ui.modules.stock_in_module',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\stock_in_module.py',
   'PYMODULE'),
  ('ui.modules',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\__init__.py',
   'PYMODULE'),
  ('ui.modules.camera_module',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\camera_module.py',
   'PYMODULE'),
  ('ui.modules.stock_out_module',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\stock_out_module.py',
   'PYMODULE'),
  ('ui.modules.weight_submission_module',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\weight_submission_module.py',
   'PYMODULE'),
  ('ui.modules.order_module',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\order_module.py',
   'PYMODULE'),
  ('config.settings',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\config\\settings.py',
   'PYMODULE'),
  ('config', '-', 'PYMODULE'),
  ('ui.styles',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\styles.py',
   'PYMODULE'),
  ('utils.auth_manager',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\utils\\auth_manager.py',
   'PYMODULE'),
  ('utils',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\utils\\__init__.py',
   'PYMODULE'),
  ('ui.login_window',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\login_window.py',
   'PYMODULE')],
 [('python313.dll', 'D:\\python\\python313.dll', 'BINARY'),
  ('PyQt6\\Qt6\\bin\\opengl32sw.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\multimedia\\ffmpegmediaplugin.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\multimedia\\ffmpegmediaplugin.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\multimedia\\windowsmediaplugin.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\multimedia\\windowsmediaplugin.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\tls\\qopensslbackend.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\tls\\qopensslbackend.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\tls\\qcertonlybackend.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\tls\\qcertonlybackend.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\networkinformation\\qnetworklistmanager.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\networkinformation\\qnetworklistmanager.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\tls\\qschannelbackend.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\tls\\qschannelbackend.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'D:\\python\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'D:\\python\\Lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('cv2\\opencv_videoio_ffmpeg4120_64.dll',
   'D:\\python\\Lib\\site-packages\\cv2\\opencv_videoio_ffmpeg4120_64.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qgif.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qsvg.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qtiff.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qtga.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qwindows.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qwebp.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qpdf.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qpdf.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qjpeg.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\iconengines\\qsvgicon.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qoffscreen.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qwbmp.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qico.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qminimal.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qicns.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'BINARY'),
  ('select.pyd', 'D:\\python\\DLLs\\select.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\python\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'D:\\python\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'D:\\python\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'D:\\python\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'D:\\python\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\python\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\python\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\python\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'D:\\python\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'D:\\python\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('PyQt6\\QtMultimediaWidgets.pyd',
   'D:\\python\\Lib\\site-packages\\PyQt6\\QtMultimediaWidgets.pyd',
   'EXTENSION'),
  ('PyQt6\\QtMultimedia.pyd',
   'D:\\python\\Lib\\site-packages\\PyQt6\\QtMultimedia.pyd',
   'EXTENSION'),
  ('PyQt6\\QtPrintSupport.pyd',
   'D:\\python\\Lib\\site-packages\\PyQt6\\QtPrintSupport.pyd',
   'EXTENSION'),
  ('PyQt6\\QtNetwork.pyd',
   'D:\\python\\Lib\\site-packages\\PyQt6\\QtNetwork.pyd',
   'EXTENSION'),
  ('PyQt6\\sip.cp313-win_amd64.pyd',
   'D:\\python\\Lib\\site-packages\\PyQt6\\sip.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp313-win_amd64.pyd',
   'D:\\python\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp313-win_amd64.pyd',
   'D:\\python\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'D:\\python\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('unicodedata.pyd', 'D:\\python\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('charset_normalizer\\md.cp313-win_amd64.pyd',
   'D:\\python\\Lib\\site-packages\\charset_normalizer\\md.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_wmi.pyd', 'D:\\python\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp313-win_amd64.pyd',
   'D:\\python\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd', 'D:\\python\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'D:\\python\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('numpy\\random\\mtrand.cp313-win_amd64.pyd',
   'D:\\python\\Lib\\site-packages\\numpy\\random\\mtrand.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp313-win_amd64.pyd',
   'D:\\python\\Lib\\site-packages\\numpy\\random\\_sfc64.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp313-win_amd64.pyd',
   'D:\\python\\Lib\\site-packages\\numpy\\random\\_philox.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp313-win_amd64.pyd',
   'D:\\python\\Lib\\site-packages\\numpy\\random\\_pcg64.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp313-win_amd64.pyd',
   'D:\\python\\Lib\\site-packages\\numpy\\random\\_mt19937.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp313-win_amd64.pyd',
   'D:\\python\\Lib\\site-packages\\numpy\\random\\bit_generator.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp313-win_amd64.pyd',
   'D:\\python\\Lib\\site-packages\\numpy\\random\\_generator.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp313-win_amd64.pyd',
   'D:\\python\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp313-win_amd64.pyd',
   'D:\\python\\Lib\\site-packages\\numpy\\random\\_common.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp313-win_amd64.pyd',
   'D:\\python\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('cv2\\cv2.pyd', 'D:\\python\\Lib\\site-packages\\cv2\\cv2.pyd', 'EXTENSION'),
  ('PyQt6\\QtGui.pyd',
   'D:\\python\\Lib\\site-packages\\PyQt6\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt6\\QtCore.pyd',
   'D:\\python\\Lib\\site-packages\\PyQt6\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt6\\QtWidgets.pyd',
   'D:\\python\\Lib\\site-packages\\PyQt6\\QtWidgets.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll', 'D:\\python\\VCRUNTIME140.dll', 'BINARY'),
  ('PyQt6\\Qt6\\bin\\avutil-59.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\avutil-59.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Multimedia.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Multimedia.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Gui.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Gui.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Core.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Core.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll', 'D:\\python\\VCRUNTIME140_1.dll', 'BINARY'),
  ('PyQt6\\Qt6\\bin\\swresample-5.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\swresample-5.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\swscale-8.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\swscale-8.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\avformat-61.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\avformat-61.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\avcodec-61.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\avcodec-61.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Network.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Network.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Svg.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Svg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Pdf.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Pdf.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Widgets.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Widgets.dll',
   'BINARY'),
  ('libssl-3.dll', 'D:\\python\\DLLs\\libssl-3.dll', 'BINARY'),
  ('libcrypto-3.dll', 'D:\\python\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('libffi-8.dll', 'D:\\python\\DLLs\\libffi-8.dll', 'BINARY'),
  ('python3.dll', 'D:\\python\\python3.dll', 'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6MultimediaWidgets.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6MultimediaWidgets.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6PrintSupport.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6PrintSupport.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140_2.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140_2.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\VCRUNTIME140.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\VCRUNTIME140_1.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140_1.dll',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140_1.dll',
   'BINARY')],
 [],
 [],
 [('api\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\__init__.py',
   'DATA'),
  ('api\\__pycache__\\__init__.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('api\\__pycache__\\auth_api.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\__pycache__\\auth_api.cpython-313.pyc',
   'DATA'),
  ('api\\__pycache__\\canteen_api.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\__pycache__\\canteen_api.cpython-313.pyc',
   'DATA'),
  ('api\\__pycache__\\order_api.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\__pycache__\\order_api.cpython-313.pyc',
   'DATA'),
  ('api\\__pycache__\\stock_api.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\__pycache__\\stock_api.cpython-313.pyc',
   'DATA'),
  ('api\\__pycache__\\stock_in_api.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\__pycache__\\stock_in_api.cpython-313.pyc',
   'DATA'),
  ('api\\__pycache__\\weight_api.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\__pycache__\\weight_api.cpython-313.pyc',
   'DATA'),
  ('api\\auth_api.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\auth_api.py',
   'DATA'),
  ('api\\canteen_api.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\canteen_api.py',
   'DATA'),
  ('api\\order_api.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\order_api.py',
   'DATA'),
  ('api\\stock_api.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\stock_api.py',
   'DATA'),
  ('api\\stock_in_api.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\stock_in_api.py',
   'DATA'),
  ('api\\weight_api.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\api\\weight_api.py',
   'DATA'),
  ('config\\GrilmorphismUI.json',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\config\\GrilmorphismUI.json',
   'DATA'),
  ('config\\__pycache__\\settings.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\config\\__pycache__\\settings.cpython-313.pyc',
   'DATA'),
  ('config\\app_config.json',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\config\\app_config.json',
   'DATA'),
  ('config\\auth_storage.json',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\config\\auth_storage.json',
   'DATA'),
  ('config\\settings.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\config\\settings.py',
   'DATA'),
  ('photos\\auto_photo_20250730_183544.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\auto_photo_20250730_183544.jpg',
   'DATA'),
  ('photos\\auto_photo_20250731_144007.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\auto_photo_20250731_144007.jpg',
   'DATA'),
  ('photos\\photo_20250730_183015.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250730_183015.jpg',
   'DATA'),
  ('photos\\photo_20250730_184018.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250730_184018.jpg',
   'DATA'),
  ('photos\\photo_20250730_184020.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250730_184020.jpg',
   'DATA'),
  ('photos\\photo_20250730_184215.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250730_184215.jpg',
   'DATA'),
  ('photos\\photo_20250731_081840.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250731_081840.jpg',
   'DATA'),
  ('photos\\photo_20250731_082920.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250731_082920.jpg',
   'DATA'),
  ('photos\\photo_20250731_085629.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250731_085629.jpg',
   'DATA'),
  ('photos\\photo_20250731_101724.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250731_101724.jpg',
   'DATA'),
  ('photos\\photo_20250731_101726.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250731_101726.jpg',
   'DATA'),
  ('photos\\photo_20250731_101728.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250731_101728.jpg',
   'DATA'),
  ('photos\\photo_20250731_111513.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250731_111513.jpg',
   'DATA'),
  ('photos\\photo_20250731_144446.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250731_144446.jpg',
   'DATA'),
  ('photos\\photo_20250731_153313.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250731_153313.jpg',
   'DATA'),
  ('photos\\photo_20250731_170444.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250731_170444.jpg',
   'DATA'),
  ('photos\\photo_20250801_194325.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_194325.jpg',
   'DATA'),
  ('photos\\photo_20250801_222155.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_222155.jpg',
   'DATA'),
  ('photos\\photo_20250801_222201.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_222201.jpg',
   'DATA'),
  ('photos\\photo_20250801_222205.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_222205.jpg',
   'DATA'),
  ('photos\\photo_20250801_222220.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_222220.jpg',
   'DATA'),
  ('photos\\photo_20250801_222440.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_222440.jpg',
   'DATA'),
  ('photos\\photo_20250801_222456.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_222456.jpg',
   'DATA'),
  ('photos\\photo_20250801_222505.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_222505.jpg',
   'DATA'),
  ('photos\\photo_20250801_222510.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_222510.jpg',
   'DATA'),
  ('photos\\photo_20250801_222518.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_222518.jpg',
   'DATA'),
  ('photos\\photo_20250801_223922.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_223922.jpg',
   'DATA'),
  ('photos\\photo_20250801_225011.jpg',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\photos\\photo_20250801_225011.jpg',
   'DATA'),
  ('ui\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\__init__.py',
   'DATA'),
  ('ui\\__pycache__\\__init__.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('ui\\__pycache__\\global_serial_manager.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\__pycache__\\global_serial_manager.cpython-313.pyc',
   'DATA'),
  ('ui\\__pycache__\\login_window.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\__pycache__\\login_window.cpython-313.pyc',
   'DATA'),
  ('ui\\__pycache__\\main_window.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\__pycache__\\main_window.cpython-313.pyc',
   'DATA'),
  ('ui\\__pycache__\\serial_manager.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\__pycache__\\serial_manager.cpython-313.pyc',
   'DATA'),
  ('ui\\__pycache__\\styles.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\__pycache__\\styles.cpython-313.pyc',
   'DATA'),
  ('ui\\dialogs\\__pycache__\\serial_config_dialog.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\dialogs\\__pycache__\\serial_config_dialog.cpython-313.pyc',
   'DATA'),
  ('ui\\dialogs\\serial_config_dialog.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\dialogs\\serial_config_dialog.py',
   'DATA'),
  ('ui\\global_serial_manager.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\global_serial_manager.py',
   'DATA'),
  ('ui\\login_window.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\login_window.py',
   'DATA'),
  ('ui\\main_window.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\main_window.py',
   'DATA'),
  ('ui\\modules\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\__init__.py',
   'DATA'),
  ('ui\\modules\\__pycache__\\__init__.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('ui\\modules\\__pycache__\\camera_module.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\__pycache__\\camera_module.cpython-313.pyc',
   'DATA'),
  ('ui\\modules\\__pycache__\\order_module.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\__pycache__\\order_module.cpython-313.pyc',
   'DATA'),
  ('ui\\modules\\__pycache__\\recipe_module.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\__pycache__\\recipe_module.cpython-313.pyc',
   'DATA'),
  ('ui\\modules\\__pycache__\\stock_in_module.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\__pycache__\\stock_in_module.cpython-313.pyc',
   'DATA'),
  ('ui\\modules\\__pycache__\\stock_out_module.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\__pycache__\\stock_out_module.cpython-313.pyc',
   'DATA'),
  ('ui\\modules\\__pycache__\\weight_submission_module.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\__pycache__\\weight_submission_module.cpython-313.pyc',
   'DATA'),
  ('ui\\modules\\camera_module.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\camera_module.py',
   'DATA'),
  ('ui\\modules\\order_module.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\order_module.py',
   'DATA'),
  ('ui\\modules\\recipe_module.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\recipe_module.py',
   'DATA'),
  ('ui\\modules\\stock_in_module.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\stock_in_module.py',
   'DATA'),
  ('ui\\modules\\stock_out_module.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\stock_out_module.py',
   'DATA'),
  ('ui\\modules\\weight_submission_module.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\modules\\weight_submission_module.py',
   'DATA'),
  ('ui\\serial_manager.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\serial_manager.py',
   'DATA'),
  ('ui\\styles.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\ui\\styles.py',
   'DATA'),
  ('utils\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\utils\\__init__.py',
   'DATA'),
  ('utils\\__pycache__\\__init__.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\utils\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('utils\\__pycache__\\auth_manager.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\utils\\__pycache__\\auth_manager.cpython-313.pyc',
   'DATA'),
  ('utils\\auth_manager.py',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\utils\\auth_manager.py',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_ko.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_zh_TW.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_da.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_it.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_en.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_ru.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_de.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_pl.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_nn.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_fr.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_pt_BR.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_tr.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_sk.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_uk.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_ja.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_hu.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_fi.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_fi.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_cs.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_bg.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_ca.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_ka.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_es.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_fa.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_fa.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_zh_CN.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_ar.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_nl.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtmultimedia_hr.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtmultimedia_hr.qm',
   'DATA'),
  ('cv2\\config-3.py',
   'D:\\python\\Lib\\site-packages\\cv2\\config-3.py',
   'DATA'),
  ('cv2\\load_config_py3.py',
   'D:\\python\\Lib\\site-packages\\cv2\\load_config_py3.py',
   'DATA'),
  ('cv2\\config.py', 'D:\\python\\Lib\\site-packages\\cv2\\config.py', 'DATA'),
  ('certifi\\py.typed',
   'D:\\python\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\python\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fr.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ja.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ar.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_nn.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sl.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ka.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_hu.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fa.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_uk.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_sk.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ja.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_zh_CN.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_gd.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_tr.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_hu.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_nn.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ko.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_zh_TW.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pt_BR.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_hr.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_es.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ko.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_lv.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_es.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_tr.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ka.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fa.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fa.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_es.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_cs.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_hr.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pt_PT.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pt_PT.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_sk.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_it.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_tr.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ka.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_bg.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_bg.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ko.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_pt_BR.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ca.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fi.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ar.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_gl.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_pl.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ru.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_en.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_hr.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sk.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ru.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_pl.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_zh_CN.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_nl.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_da.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_it.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_bg.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_de.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sv.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_gd.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_gl.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_lt.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ja.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ca.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_pt_BR.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fr.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_da.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_da.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_he.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_hu.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ru.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_nn.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_en.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_zh_TW.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_zh_CN.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_he.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_uk.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_sl.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_uk.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pl.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_de.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_nl.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_nl.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_cs.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_it.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ca.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_lv.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fi.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ar.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_cs.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_fr.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_zh_TW.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_en.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_de.qm',
   'D:\\python\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_de.qm',
   'DATA'),
  ('numpy-2.2.6.dist-info\\RECORD',
   'D:\\python\\Lib\\site-packages\\numpy-2.2.6.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.2.6.dist-info\\DELVEWHEEL',
   'D:\\python\\Lib\\site-packages\\numpy-2.2.6.dist-info\\DELVEWHEEL',
   'DATA'),
  ('numpy-2.2.6.dist-info\\METADATA',
   'D:\\python\\Lib\\site-packages\\numpy-2.2.6.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.2.6.dist-info\\entry_points.txt',
   'D:\\python\\Lib\\site-packages\\numpy-2.2.6.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\WHEEL',
   'D:\\python\\Lib\\site-packages\\numpy-2.2.6.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.2.6.dist-info\\LICENSE.txt',
   'D:\\python\\Lib\\site-packages\\numpy-2.2.6.dist-info\\LICENSE.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\REQUESTED',
   'D:\\python\\Lib\\site-packages\\numpy-2.2.6.dist-info\\REQUESTED',
   'DATA'),
  ('numpy-2.2.6.dist-info\\INSTALLER',
   'D:\\python\\Lib\\site-packages\\numpy-2.2.6.dist-info\\INSTALLER',
   'DATA'),
  ('cv2\\__init__.py',
   'D:\\python\\Lib\\site-packages\\cv2\\__init__.py',
   'DATA'),
  ('cv2\\version.py',
   'D:\\python\\Lib\\site-packages\\cv2\\version.py',
   'DATA'),
  ('cv2\\utils\\__init__.py',
   'D:\\python\\Lib\\site-packages\\cv2\\utils\\__init__.py',
   'DATA'),
  ('cv2\\typing\\__init__.py',
   'D:\\python\\Lib\\site-packages\\cv2\\typing\\__init__.py',
   'DATA'),
  ('cv2\\misc\\version.py',
   'D:\\python\\Lib\\site-packages\\cv2\\misc\\version.py',
   'DATA'),
  ('cv2\\misc\\__init__.py',
   'D:\\python\\Lib\\site-packages\\cv2\\misc\\__init__.py',
   'DATA'),
  ('cv2\\mat_wrapper\\__init__.py',
   'D:\\python\\Lib\\site-packages\\cv2\\mat_wrapper\\__init__.py',
   'DATA'),
  ('cv2\\gapi\\__init__.py',
   'D:\\python\\Lib\\site-packages\\cv2\\gapi\\__init__.py',
   'DATA'),
  ('cv2\\data\\__init__.py',
   'D:\\python\\Lib\\site-packages\\cv2\\data\\__init__.py',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\MaiCui\\MaiCui\\build\\MaiCui\\base_library.zip',
   'DATA')],
 [('cv2', 'D:\\python\\Lib\\site-packages\\cv2\\__init__.py', 'PYMODULE'),
  ('cv2.version',
   'D:\\python\\Lib\\site-packages\\cv2\\version.py',
   'PYMODULE'),
  ('cv2.utils',
   'D:\\python\\Lib\\site-packages\\cv2\\utils\\__init__.py',
   'PYMODULE'),
  ('cv2.typing',
   'D:\\python\\Lib\\site-packages\\cv2\\typing\\__init__.py',
   'PYMODULE'),
  ('cv2.dnn', '-', 'PYMODULE'),
  ('cv2.gapi.wip.draw', '-', 'PYMODULE'),
  ('cv2.gapi.wip', '-', 'PYMODULE'),
  ('cv2.misc.version',
   'D:\\python\\Lib\\site-packages\\cv2\\misc\\version.py',
   'PYMODULE'),
  ('cv2.misc',
   'D:\\python\\Lib\\site-packages\\cv2\\misc\\__init__.py',
   'PYMODULE'),
  ('cv2.mat_wrapper',
   'D:\\python\\Lib\\site-packages\\cv2\\mat_wrapper\\__init__.py',
   'PYMODULE'),
  ('cv2.gapi',
   'D:\\python\\Lib\\site-packages\\cv2\\gapi\\__init__.py',
   'PYMODULE'),
  ('cv2.data',
   'D:\\python\\Lib\\site-packages\\cv2\\data\\__init__.py',
   'PYMODULE'),
  ('cv2.config-3',
   'D:\\python\\Lib\\site-packages\\cv2\\config-3.py',
   'PYMODULE'),
  ('cv2.config', 'D:\\python\\Lib\\site-packages\\cv2\\config.py', 'PYMODULE'),
  ('cv2.load_config_py3',
   'D:\\python\\Lib\\site-packages\\cv2\\load_config_py3.py',
   'PYMODULE'),
  ('ntpath', 'D:\\python\\Lib\\ntpath.py', 'PYMODULE'),
  ('sre_compile', 'D:\\python\\Lib\\sre_compile.py', 'PYMODULE'),
  ('operator', 'D:\\python\\Lib\\operator.py', 'PYMODULE'),
  ('linecache', 'D:\\python\\Lib\\linecache.py', 'PYMODULE'),
  ('keyword', 'D:\\python\\Lib\\keyword.py', 'PYMODULE'),
  ('collections', 'D:\\python\\Lib\\collections\\__init__.py', 'PYMODULE'),
  ('re._parser', 'D:\\python\\Lib\\re\\_parser.py', 'PYMODULE'),
  ('re._constants', 'D:\\python\\Lib\\re\\_constants.py', 'PYMODULE'),
  ('re._compiler', 'D:\\python\\Lib\\re\\_compiler.py', 'PYMODULE'),
  ('re._casefix', 'D:\\python\\Lib\\re\\_casefix.py', 'PYMODULE'),
  ('re', 'D:\\python\\Lib\\re\\__init__.py', 'PYMODULE'),
  ('reprlib', 'D:\\python\\Lib\\reprlib.py', 'PYMODULE'),
  ('codecs', 'D:\\python\\Lib\\codecs.py', 'PYMODULE'),
  ('types', 'D:\\python\\Lib\\types.py', 'PYMODULE'),
  ('weakref', 'D:\\python\\Lib\\weakref.py', 'PYMODULE'),
  ('abc', 'D:\\python\\Lib\\abc.py', 'PYMODULE'),
  ('enum', 'D:\\python\\Lib\\enum.py', 'PYMODULE'),
  ('sre_constants', 'D:\\python\\Lib\\sre_constants.py', 'PYMODULE'),
  ('heapq', 'D:\\python\\Lib\\heapq.py', 'PYMODULE'),
  ('warnings', 'D:\\python\\Lib\\warnings.py', 'PYMODULE'),
  ('genericpath', 'D:\\python\\Lib\\genericpath.py', 'PYMODULE'),
  ('locale', 'D:\\python\\Lib\\locale.py', 'PYMODULE'),
  ('stat', 'D:\\python\\Lib\\stat.py', 'PYMODULE'),
  ('encodings.zlib_codec',
   'D:\\python\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec', 'D:\\python\\Lib\\encodings\\uu_codec.py', 'PYMODULE'),
  ('encodings.utf_8_sig',
   'D:\\python\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8', 'D:\\python\\Lib\\encodings\\utf_8.py', 'PYMODULE'),
  ('encodings.utf_7', 'D:\\python\\Lib\\encodings\\utf_7.py', 'PYMODULE'),
  ('encodings.utf_32_le',
   'D:\\python\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'D:\\python\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32', 'D:\\python\\Lib\\encodings\\utf_32.py', 'PYMODULE'),
  ('encodings.utf_16_le',
   'D:\\python\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'D:\\python\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16', 'D:\\python\\Lib\\encodings\\utf_16.py', 'PYMODULE'),
  ('encodings.unicode_escape',
   'D:\\python\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'D:\\python\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620', 'D:\\python\\Lib\\encodings\\tis_620.py', 'PYMODULE'),
  ('encodings.shift_jisx0213',
   'D:\\python\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'D:\\python\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'D:\\python\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13', 'D:\\python\\Lib\\encodings\\rot_13.py', 'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'D:\\python\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'D:\\python\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode', 'D:\\python\\Lib\\encodings\\punycode.py', 'PYMODULE'),
  ('encodings.ptcp154', 'D:\\python\\Lib\\encodings\\ptcp154.py', 'PYMODULE'),
  ('encodings.palmos', 'D:\\python\\Lib\\encodings\\palmos.py', 'PYMODULE'),
  ('encodings.oem', 'D:\\python\\Lib\\encodings\\oem.py', 'PYMODULE'),
  ('encodings.mbcs', 'D:\\python\\Lib\\encodings\\mbcs.py', 'PYMODULE'),
  ('encodings.mac_turkish',
   'D:\\python\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'D:\\python\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'D:\\python\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'D:\\python\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'D:\\python\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'D:\\python\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'D:\\python\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'D:\\python\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'D:\\python\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'D:\\python\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1', 'D:\\python\\Lib\\encodings\\latin_1.py', 'PYMODULE'),
  ('encodings.kz1048', 'D:\\python\\Lib\\encodings\\kz1048.py', 'PYMODULE'),
  ('encodings.koi8_u', 'D:\\python\\Lib\\encodings\\koi8_u.py', 'PYMODULE'),
  ('encodings.koi8_t', 'D:\\python\\Lib\\encodings\\koi8_t.py', 'PYMODULE'),
  ('encodings.koi8_r', 'D:\\python\\Lib\\encodings\\koi8_r.py', 'PYMODULE'),
  ('encodings.johab', 'D:\\python\\Lib\\encodings\\johab.py', 'PYMODULE'),
  ('encodings.iso8859_9',
   'D:\\python\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'D:\\python\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'D:\\python\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'D:\\python\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'D:\\python\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'D:\\python\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'D:\\python\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'D:\\python\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'D:\\python\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'D:\\python\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'D:\\python\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'D:\\python\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'D:\\python\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'D:\\python\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'D:\\python\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'D:\\python\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'D:\\python\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'D:\\python\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'D:\\python\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'D:\\python\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'D:\\python\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'D:\\python\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna', 'D:\\python\\Lib\\encodings\\idna.py', 'PYMODULE'),
  ('encodings.hz', 'D:\\python\\Lib\\encodings\\hz.py', 'PYMODULE'),
  ('encodings.hp_roman8',
   'D:\\python\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'D:\\python\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk', 'D:\\python\\Lib\\encodings\\gbk.py', 'PYMODULE'),
  ('encodings.gb2312', 'D:\\python\\Lib\\encodings\\gb2312.py', 'PYMODULE'),
  ('encodings.gb18030', 'D:\\python\\Lib\\encodings\\gb18030.py', 'PYMODULE'),
  ('encodings.euc_kr', 'D:\\python\\Lib\\encodings\\euc_kr.py', 'PYMODULE'),
  ('encodings.euc_jp', 'D:\\python\\Lib\\encodings\\euc_jp.py', 'PYMODULE'),
  ('encodings.euc_jisx0213',
   'D:\\python\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'D:\\python\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950', 'D:\\python\\Lib\\encodings\\cp950.py', 'PYMODULE'),
  ('encodings.cp949', 'D:\\python\\Lib\\encodings\\cp949.py', 'PYMODULE'),
  ('encodings.cp932', 'D:\\python\\Lib\\encodings\\cp932.py', 'PYMODULE'),
  ('encodings.cp875', 'D:\\python\\Lib\\encodings\\cp875.py', 'PYMODULE'),
  ('encodings.cp874', 'D:\\python\\Lib\\encodings\\cp874.py', 'PYMODULE'),
  ('encodings.cp869', 'D:\\python\\Lib\\encodings\\cp869.py', 'PYMODULE'),
  ('encodings.cp866', 'D:\\python\\Lib\\encodings\\cp866.py', 'PYMODULE'),
  ('encodings.cp865', 'D:\\python\\Lib\\encodings\\cp865.py', 'PYMODULE'),
  ('encodings.cp864', 'D:\\python\\Lib\\encodings\\cp864.py', 'PYMODULE'),
  ('encodings.cp863', 'D:\\python\\Lib\\encodings\\cp863.py', 'PYMODULE'),
  ('encodings.cp862', 'D:\\python\\Lib\\encodings\\cp862.py', 'PYMODULE'),
  ('encodings.cp861', 'D:\\python\\Lib\\encodings\\cp861.py', 'PYMODULE'),
  ('encodings.cp860', 'D:\\python\\Lib\\encodings\\cp860.py', 'PYMODULE'),
  ('encodings.cp858', 'D:\\python\\Lib\\encodings\\cp858.py', 'PYMODULE'),
  ('encodings.cp857', 'D:\\python\\Lib\\encodings\\cp857.py', 'PYMODULE'),
  ('encodings.cp856', 'D:\\python\\Lib\\encodings\\cp856.py', 'PYMODULE'),
  ('encodings.cp855', 'D:\\python\\Lib\\encodings\\cp855.py', 'PYMODULE'),
  ('encodings.cp852', 'D:\\python\\Lib\\encodings\\cp852.py', 'PYMODULE'),
  ('encodings.cp850', 'D:\\python\\Lib\\encodings\\cp850.py', 'PYMODULE'),
  ('encodings.cp775', 'D:\\python\\Lib\\encodings\\cp775.py', 'PYMODULE'),
  ('encodings.cp737', 'D:\\python\\Lib\\encodings\\cp737.py', 'PYMODULE'),
  ('encodings.cp720', 'D:\\python\\Lib\\encodings\\cp720.py', 'PYMODULE'),
  ('encodings.cp500', 'D:\\python\\Lib\\encodings\\cp500.py', 'PYMODULE'),
  ('encodings.cp437', 'D:\\python\\Lib\\encodings\\cp437.py', 'PYMODULE'),
  ('encodings.cp424', 'D:\\python\\Lib\\encodings\\cp424.py', 'PYMODULE'),
  ('encodings.cp273', 'D:\\python\\Lib\\encodings\\cp273.py', 'PYMODULE'),
  ('encodings.cp1258', 'D:\\python\\Lib\\encodings\\cp1258.py', 'PYMODULE'),
  ('encodings.cp1257', 'D:\\python\\Lib\\encodings\\cp1257.py', 'PYMODULE'),
  ('encodings.cp1256', 'D:\\python\\Lib\\encodings\\cp1256.py', 'PYMODULE'),
  ('encodings.cp1255', 'D:\\python\\Lib\\encodings\\cp1255.py', 'PYMODULE'),
  ('encodings.cp1254', 'D:\\python\\Lib\\encodings\\cp1254.py', 'PYMODULE'),
  ('encodings.cp1253', 'D:\\python\\Lib\\encodings\\cp1253.py', 'PYMODULE'),
  ('encodings.cp1252', 'D:\\python\\Lib\\encodings\\cp1252.py', 'PYMODULE'),
  ('encodings.cp1251', 'D:\\python\\Lib\\encodings\\cp1251.py', 'PYMODULE'),
  ('encodings.cp1250', 'D:\\python\\Lib\\encodings\\cp1250.py', 'PYMODULE'),
  ('encodings.cp1140', 'D:\\python\\Lib\\encodings\\cp1140.py', 'PYMODULE'),
  ('encodings.cp1125', 'D:\\python\\Lib\\encodings\\cp1125.py', 'PYMODULE'),
  ('encodings.cp1026', 'D:\\python\\Lib\\encodings\\cp1026.py', 'PYMODULE'),
  ('encodings.cp1006', 'D:\\python\\Lib\\encodings\\cp1006.py', 'PYMODULE'),
  ('encodings.cp037', 'D:\\python\\Lib\\encodings\\cp037.py', 'PYMODULE'),
  ('encodings.charmap', 'D:\\python\\Lib\\encodings\\charmap.py', 'PYMODULE'),
  ('encodings.bz2_codec',
   'D:\\python\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'D:\\python\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5', 'D:\\python\\Lib\\encodings\\big5.py', 'PYMODULE'),
  ('encodings.base64_codec',
   'D:\\python\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii', 'D:\\python\\Lib\\encodings\\ascii.py', 'PYMODULE'),
  ('encodings.aliases', 'D:\\python\\Lib\\encodings\\aliases.py', 'PYMODULE'),
  ('encodings', 'D:\\python\\Lib\\encodings\\__init__.py', 'PYMODULE'),
  ('io', 'D:\\python\\Lib\\io.py', 'PYMODULE'),
  ('_collections_abc', 'D:\\python\\Lib\\_collections_abc.py', 'PYMODULE'),
  ('posixpath', 'D:\\python\\Lib\\posixpath.py', 'PYMODULE'),
  ('_weakrefset', 'D:\\python\\Lib\\_weakrefset.py', 'PYMODULE'),
  ('functools', 'D:\\python\\Lib\\functools.py', 'PYMODULE'),
  ('copyreg', 'D:\\python\\Lib\\copyreg.py', 'PYMODULE'),
  ('sre_parse', 'D:\\python\\Lib\\sre_parse.py', 'PYMODULE'),
  ('traceback', 'D:\\python\\Lib\\traceback.py', 'PYMODULE'),
  ('os', 'D:\\python\\Lib\\os.py', 'PYMODULE')])
