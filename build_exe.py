#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧食堂管理系统 - 完整打包脚本
Smart Canteen Management System - Complete Build Script
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_step(step_name):
    """打印步骤信息"""
    print(f"\n{'='*60}")
    print(f"🚀 {step_name}")
    print(f"{'='*60}")

def run_command(command, description):
    """运行命令并处理错误"""
    print(f"\n📋 {description}")
    print(f"💻 执行命令: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True, encoding='utf-8')
        if result.stdout:
            print(f"✅ 输出: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 命令执行失败: {e}")
        if e.stdout:
            print(f"标准输出: {e.stdout}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ 执行异常: {e}")
        return False

def check_python_version():
    """检查Python版本"""
    print_step("检查Python环境")
    
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    
    print("✅ Python版本符合要求")
    return True

def install_dependencies():
    """安装依赖包"""
    print_step("安装依赖包")
    
    # 升级pip
    if not run_command("python -m pip install --upgrade pip", "升级pip"):
        return False
    
    # 安装基础依赖
    dependencies = [
        "PyQt6>=6.4.0",
        "requests>=2.28.0", 
        "opencv-python>=4.8.0",
        "pyserial>=3.5",
        "numpy>=1.24.0",
        "PyInstaller>=5.13.0"
    ]
    
    for dep in dependencies:
        if not run_command(f"pip install {dep}", f"安装 {dep}"):
            print(f"❌ 安装 {dep} 失败")
            return False
    
    print("✅ 所有依赖包安装完成")
    return True

def verify_imports():
    """验证关键模块导入"""
    print_step("验证模块导入")
    
    modules_to_test = [
        ("PyQt6.QtWidgets", "PyQt6 GUI框架"),
        ("PyQt6.QtCore", "PyQt6 核心模块"),
        ("PyQt6.QtGui", "PyQt6 GUI模块"),
        ("requests", "HTTP请求库"),
        ("cv2", "OpenCV摄像头库"),
        ("serial", "串口通信库"),
        ("numpy", "数值计算库"),
        ("PyInstaller", "打包工具")
    ]
    
    failed_modules = []
    
    for module_name, description in modules_to_test:
        try:
            __import__(module_name)
            print(f"✅ {description} ({module_name}) - 导入成功")
        except ImportError as e:
            print(f"❌ {description} ({module_name}) - 导入失败: {e}")
            failed_modules.append(module_name)
    
    if failed_modules:
        print(f"\n❌ 以下模块导入失败: {', '.join(failed_modules)}")
        return False
    
    print("\n✅ 所有关键模块导入成功")
    return True

def clean_build_dirs():
    """清理构建目录"""
    print_step("清理构建目录")
    
    dirs_to_clean = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"✅ 清理目录: {dir_name}")
            except Exception as e:
                print(f"⚠️  清理目录 {dir_name} 失败: {e}")
        else:
            print(f"📁 目录不存在: {dir_name}")

def build_executable():
    """构建可执行文件"""
    print_step("构建可执行文件")
    
    # 检查spec文件是否存在
    spec_file = "MaiCui.spec"
    if not os.path.exists(spec_file):
        print(f"❌ 找不到配置文件: {spec_file}")
        return False
    
    # 使用PyInstaller构建
    build_command = f"pyinstaller {spec_file} --clean --noconfirm"
    
    if not run_command(build_command, "使用PyInstaller构建可执行文件"):
        return False
    
    # 检查构建结果
    exe_path = os.path.join("dist", "智慧食堂管理系统.exe")
    dir_path = os.path.join("dist", "智慧食堂管理系统_目录版")
    
    if os.path.exists(exe_path):
        size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
        print(f"✅ 单文件版本构建成功: {exe_path} ({size:.1f} MB)")
    else:
        print(f"❌ 单文件版本构建失败: {exe_path}")
    
    if os.path.exists(dir_path):
        print(f"✅ 目录版本构建成功: {dir_path}")
    else:
        print(f"❌ 目录版本构建失败: {dir_path}")
    
    return os.path.exists(exe_path) or os.path.exists(dir_path)

def copy_additional_files():
    """复制额外文件到发布目录"""
    print_step("复制额外文件")
    
    # 创建release目录
    release_dir = "release"
    if os.path.exists(release_dir):
        shutil.rmtree(release_dir)
    os.makedirs(release_dir, exist_ok=True)
    
    # 复制可执行文件
    exe_src = os.path.join("dist", "智慧食堂管理系统.exe")
    dir_src = os.path.join("dist", "智慧食堂管理系统_目录版")
    
    if os.path.exists(exe_src):
        shutil.copy2(exe_src, release_dir)
        print(f"✅ 复制单文件版本到 {release_dir}")
    
    if os.path.exists(dir_src):
        shutil.copytree(dir_src, os.path.join(release_dir, "智慧食堂管理系统_目录版"))
        print(f"✅ 复制目录版本到 {release_dir}")
    
    # 复制配置文件
    config_src = "config"
    config_dst = os.path.join(release_dir, "config")
    if os.path.exists(config_src):
        shutil.copytree(config_src, config_dst)
        print(f"✅ 复制配置文件到 {release_dir}")
    
    # 创建启动脚本
    start_script = os.path.join(release_dir, "启动程序.bat")
    with open(start_script, 'w', encoding='utf-8') as f:
        f.write('@echo off\n')
        f.write('echo 启动智慧食堂管理系统...\n')
        f.write('智慧食堂管理系统.exe\n')
        f.write('pause\n')
    print(f"✅ 创建启动脚本: {start_script}")
    
    # 创建使用说明
    readme_file = os.path.join(release_dir, "使用说明.txt")
    with open(readme_file, 'w', encoding='utf-8') as f:
        f.write("智慧食堂管理系统 使用说明\n")
        f.write("=" * 40 + "\n\n")
        f.write("1. 运行方式:\n")
        f.write("   - 双击 '智慧食堂管理系统.exe' 直接运行\n")
        f.write("   - 或双击 '启动程序.bat' 运行\n\n")
        f.write("2. 系统要求:\n")
        f.write("   - Windows 10/11 操作系统\n")
        f.write("   - 摄像头设备（可选）\n")
        f.write("   - 串口设备（可选）\n\n")
        f.write("3. 功能说明:\n")
        f.write("   - 支持摄像头拍照功能\n")
        f.write("   - 支持串口重量秤数据读取\n")
        f.write("   - 支持在线API数据同步\n\n")
        f.write("4. 注意事项:\n")
        f.write("   - 首次运行需要网络连接进行登录\n")
        f.write("   - 配置文件位于config目录下\n")
        f.write("   - 拍照文件保存在photos目录下\n")
    print(f"✅ 创建使用说明: {readme_file}")
    
    return True

def main():
    """主函数"""
    print("🎯 智慧食堂管理系统 - 完整打包脚本")
    print("=" * 60)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 安装依赖
    if not install_dependencies():
        print("\n❌ 依赖安装失败，请检查网络连接和权限")
        sys.exit(1)
    
    # 验证导入
    if not verify_imports():
        print("\n❌ 模块验证失败，请检查依赖安装")
        sys.exit(1)
    
    # 清理构建目录
    clean_build_dirs()
    
    # 构建可执行文件
    if not build_executable():
        print("\n❌ 构建失败，请检查错误信息")
        sys.exit(1)
    
    # 复制额外文件
    if not copy_additional_files():
        print("\n❌ 文件复制失败")
        sys.exit(1)
    
    print_step("构建完成")
    print("✅ 智慧食堂管理系统打包完成！")
    print("\n📁 输出文件:")
    print("   - release/智慧食堂管理系统.exe (单文件版本)")
    print("   - release/智慧食堂管理系统_目录版/ (目录版本)")
    print("   - release/config/ (配置文件)")
    print("   - release/启动程序.bat (启动脚本)")
    print("   - release/使用说明.txt (使用说明)")
    print("\n🚀 可以直接运行 release/智慧食堂管理系统.exe")

if __name__ == "__main__":
    main()
