#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
出库管理模块
Stock Out Management Module
"""

import sys
import os
import json
from datetime import datetime, date
from typing import Dict, Any, Optional, List
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QLineEdit, QTextEdit, QMessageBox, QFrame, QScrollArea,
    QGridLayout, QSpacerItem, QSizePolicy, QProgressBar,
    QComboBox, QTableWidget, QTableWidgetItem, QHeaderView,
    QDateEdit, QSpinBox, QGroupBox, QSplitter, QTabWidget
)
from PyQt6.QtCore import Qt, QTimer, QThread, pyqtSignal, QDate, QMutex
from PyQt6.QtGui import QFont, QPalette, QColor, QPixmap

# 导入API
try:
    from api.stock_api import StockAPI
except ImportError:
    StockAPI = None
    print("警告: 库存API导入失败")

# 导入摄像头和串口模块
try:
    from .camera_module import CameraModule
except ImportError:
    CameraModule = None
    print("警告: 摄像头模块导入失败")

try:
    from .weight_submission_module import SerialWorker
except ImportError:
    SerialWorker = None
    print("警告: 串口模块导入失败")

# 暂时禁用全局串口管理器，回退到原始方案
global_serial_manager = None

from ..styles import GlassmorphismStyles


class StockDataLoader(QThread):
    """库存数据加载线程"""
    data_loaded = pyqtSignal(str, dict)  # 数据类型, 数据
    error_occurred = pyqtSignal(str)
    progress_updated = pyqtSignal(int)

    def __init__(self, api: StockAPI, data_type: str):
        super().__init__()
        self.api = api
        self.data_type = data_type

    def run(self):
        """运行数据加载"""
        try:
            self.progress_updated.emit(20)

            # 检查API状态
            if not self.api:
                self.error_occurred.emit(f"API实例为空")
                return

            if not hasattr(self.api, 'access_token') or not self.api.access_token:
                self.error_occurred.emit(f"API未认证，请先登录")
                return

            if self.data_type == "depot":
                result = self.api.get_depot_list()
            elif self.data_type == "project":
                result = self.api.get_project_list()
            elif self.data_type == "meal_time":
                result = self.api.get_meal_time_list()
            elif self.data_type == "stock":
                result = self.api.get_stock_list()
            else:
                raise ValueError(f"未知的数据类型: {self.data_type}")

            self.progress_updated.emit(80)

            if result.get('code') == 200:
                self.data_loaded.emit(self.data_type, result)
            else:
                error_msg = f"加载{self.data_type}失败: {result.get('msg', '未知错误')} (code: {result.get('code')})"
                self.error_occurred.emit(error_msg)

            self.progress_updated.emit(100)

        except Exception as e:
            error_msg = f"加载{self.data_type}时发生错误: {str(e)}"
            self.error_occurred.emit(error_msg)


class StockOutSubmitter(QThread):
    """出库提交线程"""
    submit_completed = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    progress_updated = pyqtSignal(int)

    def __init__(self, api, submit_data):
        super().__init__()
        self.api = api
        self.submit_data = submit_data

    def run(self):
        """运行出库提交"""
        try:
            self.progress_updated.emit(30)
            
            # 验证数据
            validation = self.api.validate_stock_out_data(**self.submit_data)
            if not validation['valid']:
                self.error_occurred.emit(f"数据验证失败: {', '.join(validation['errors'])}")
                return
            
            self.progress_updated.emit(60)
            
            # 提交出库申请
            result = self.api.submit_stock_out(**self.submit_data)
            
            self.progress_updated.emit(90)
            
            if result.get('code') == 200:
                self.submit_completed.emit(result)
            else:
                self.error_occurred.emit(f"出库提交失败: {result.get('msg', '未知错误')}")
            
            self.progress_updated.emit(100)
            
        except Exception as e:
            self.error_occurred.emit(f"出库提交时发生错误: {str(e)}")


class StockOutModule(QWidget):
    """出库管理模块"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.api = None
        
        # 数据存储
        self.depot_data = []
        self.project_data = []
        self.meal_time_data = []
        self.stock_data = []
        self.selected_stock_items = []  # 选中的库存商品
        
        # 线程管理
        self.loader_threads = {}
        self.submit_thread = None
        
        # 串口和摄像头
        self.serial_worker = None
        self.camera_module = None
        self.current_weight = "0.0"
        self.captured_photos = []
        
        self.init_ui()
        self.apply_styles()
        self.setup_connections()

        # 设置串口连接
        self.setup_serial_connection()
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # 标题
        self.create_title(layout)
        
        # 主要内容区域
        self.create_main_content(layout)
        
        # 底部操作区域
        self.create_bottom_actions(layout)
        
    def create_title(self, parent_layout):
        """创建标题区域"""
        title_frame = QFrame()
        title_frame.setObjectName("titleFrame")
        title_layout = QHBoxLayout(title_frame)
        
        # 标题
        title_label = QLabel("📦 出库管理")
        title_label.setObjectName("titleLabel")
        title_label.setFont(QFont("Inter", 24, QFont.Weight.Bold))
        
        # 状态指示器
        self.status_label = QLabel("就绪")
        self.status_label.setObjectName("statusLabel")
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximum(100)
        
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        title_layout.addWidget(self.status_label)
        title_layout.addWidget(self.progress_bar)
        
        parent_layout.addWidget(title_frame)
        
    def create_main_content(self, parent_layout):
        """创建主要内容区域"""
        # 使用分割器创建左右布局
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：选择区域
        left_widget = self.create_selection_area()
        splitter.addWidget(left_widget)
        
        # 右侧：商品和操作区域
        right_widget = self.create_operation_area()
        splitter.addWidget(right_widget)
        
        # 设置分割比例
        splitter.setSizes([400, 600])
        
        parent_layout.addWidget(splitter)
        
    def create_selection_area(self):
        """创建选择区域"""
        widget = QFrame()
        widget.setObjectName("selectionFrame")
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        
        # 基础信息选择
        self.create_basic_selection(layout)
        
        # 库存商品列表
        self.create_stock_list(layout)
        
        return widget
        
    def create_basic_selection(self, parent_layout):
        """创建基础信息选择"""
        group = QGroupBox("📋 基础信息")
        group.setObjectName("basicGroup")
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # 出库日期
        layout.addWidget(QLabel("出库日期:"), 0, 0)
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        layout.addWidget(self.date_edit, 0, 1)
        
        # 仓库选择
        layout.addWidget(QLabel("仓库:"), 1, 0)
        self.depot_combo = QComboBox()
        self.depot_combo.addItem("请选择仓库", "")
        self.depot_combo.setMinimumWidth(250)  # 设置最小宽度
        layout.addWidget(self.depot_combo, 1, 1)

        # 项目选择
        layout.addWidget(QLabel("项目:"), 2, 0)
        self.project_combo = QComboBox()
        self.project_combo.addItem("请选择项目", "")
        self.project_combo.setMinimumWidth(250)  # 设置最小宽度
        layout.addWidget(self.project_combo, 2, 1)

        # 餐别选择
        layout.addWidget(QLabel("餐别:"), 3, 0)
        self.meal_time_combo = QComboBox()
        self.meal_time_combo.addItem("请选择餐别", "")
        self.meal_time_combo.setMinimumWidth(250)  # 设置最小宽度
        layout.addWidget(self.meal_time_combo, 3, 1)
        
        # 人数
        layout.addWidget(QLabel("人数:"), 4, 0)
        self.people_spin = QSpinBox()
        self.people_spin.setRange(1, 9999)
        self.people_spin.setValue(100)
        layout.addWidget(self.people_spin, 4, 1)
        
        # 刷新按钮
        refresh_btn = QPushButton("🔄 刷新数据")
        refresh_btn.setObjectName("refreshBtn")
        refresh_btn.clicked.connect(self.load_all_data)
        layout.addWidget(refresh_btn, 5, 0, 1, 2)
        
        parent_layout.addWidget(group)
        
    def create_stock_list(self, parent_layout):
        """创建库存商品列表"""
        group = QGroupBox("📦 库存商品")
        group.setObjectName("stockGroup")
        layout = QVBoxLayout(group)
        
        # 搜索框
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("搜索:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("输入商品名称或代码...")
        search_layout.addWidget(self.search_edit)
        layout.addLayout(search_layout)
        
        # 库存表格
        self.stock_table = QTableWidget()
        self.stock_table.setColumnCount(4)
        self.stock_table.setHorizontalHeaderLabels(["商品代码", "商品名称", "单位", "库存数量"])

        # 设置表格属性
        header = self.stock_table.horizontalHeader()
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # 商品名称列拉伸
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # 商品代码列自适应
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # 单位列自适应
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # 库存数量列自适应

        self.stock_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.stock_table.setAlternatingRowColors(True)
        self.stock_table.setMinimumHeight(300)  # 设置最小高度

        # 设置行高
        self.stock_table.verticalHeader().setDefaultSectionSize(35)

        layout.addWidget(self.stock_table)

        parent_layout.addWidget(group)

    def create_operation_area(self):
        """创建操作区域"""
        widget = QFrame()
        widget.setObjectName("operationFrame")
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)

        # 选中商品信息
        self.create_selected_items(layout)

        # 重量和照片区域
        self.create_weight_photo_area(layout)

        return widget

    def create_selected_items(self, parent_layout):
        """创建选中商品区域"""
        group = QGroupBox("🛒 选中商品")
        group.setObjectName("selectedGroup")
        layout = QVBoxLayout(group)

        # 选中商品表格
        self.selected_table = QTableWidget()
        self.selected_table.setColumnCount(5)
        self.selected_table.setHorizontalHeaderLabels(["商品代码", "商品名称", "出库数量", "单位", "操作"])

        # 设置表格属性
        header = self.selected_table.horizontalHeader()
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # 商品代码列自适应
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # 出库数量列自适应
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # 单位列自适应
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # 操作列自适应

        self.selected_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.selected_table.setMinimumHeight(200)  # 设置最小高度
        self.selected_table.setMaximumHeight(400)  # 设置最大高度

        # 设置行高
        self.selected_table.verticalHeader().setDefaultSectionSize(40)

        layout.addWidget(self.selected_table)

        # 添加商品按钮
        add_btn = QPushButton("➕ 添加选中商品")
        add_btn.setObjectName("addBtn")
        add_btn.clicked.connect(self.add_selected_stock)
        layout.addWidget(add_btn)

        parent_layout.addWidget(group)

    def create_weight_photo_area(self, parent_layout):
        """创建重量和照片区域"""
        # 使用标签页
        tab_widget = QTabWidget()
        tab_widget.setObjectName("operationTabs")

        # 重量标签页
        weight_tab = self.create_weight_tab()
        tab_widget.addTab(weight_tab, "⚖️ 重量")

        # 照片标签页
        photo_tab = self.create_photo_tab()
        tab_widget.addTab(photo_tab, "📷 照片")

        parent_layout.addWidget(tab_widget)

    def create_weight_tab(self):
        """创建重量标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)

        # 串口连接状态
        status_frame = QFrame()
        status_frame.setObjectName("statusFrame")
        status_layout = QHBoxLayout(status_frame)

        self.serial_status_label = QLabel("串口状态: 未连接")
        self.serial_status_label.setObjectName("serialStatus")

        self.connect_btn = QPushButton("🔌 连接串口")
        self.connect_btn.setObjectName("connectBtn")
        self.connect_btn.clicked.connect(self.toggle_serial_connection)

        status_layout.addWidget(self.serial_status_label)
        status_layout.addStretch()
        status_layout.addWidget(self.connect_btn)

        layout.addWidget(status_frame)

        # 重量显示
        weight_frame = QFrame()
        weight_frame.setObjectName("weightFrame")
        weight_layout = QVBoxLayout(weight_frame)

        weight_title = QLabel("当前重量")
        weight_title.setObjectName("weightTitle")
        weight_title.setFont(QFont("Inter", 16, QFont.Weight.Bold))
        weight_title.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.weight_display = QLabel("0.0 kg")
        self.weight_display.setObjectName("weightDisplay")
        self.weight_display.setFont(QFont("Inter", 36, QFont.Weight.Bold))
        self.weight_display.setAlignment(Qt.AlignmentFlag.AlignCenter)

        weight_layout.addWidget(weight_title)
        weight_layout.addWidget(self.weight_display)

        layout.addWidget(weight_frame)

        # 手动输入重量
        manual_frame = QFrame()
        manual_frame.setObjectName("manualFrame")
        manual_layout = QHBoxLayout(manual_frame)

        manual_layout.addWidget(QLabel("手动输入:"))
        self.manual_weight_edit = QLineEdit()
        self.manual_weight_edit.setPlaceholderText("输入重量...")
        manual_layout.addWidget(self.manual_weight_edit)

        manual_btn = QPushButton("✅ 确认重量")
        manual_btn.setObjectName("manualBtn")
        manual_btn.clicked.connect(self.set_manual_weight)
        manual_layout.addWidget(manual_btn)

        layout.addWidget(manual_frame)

        layout.addStretch()
        return widget

    def create_photo_tab(self):
        """创建照片标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)

        # 摄像头预览
        if CameraModule:
            self.camera_module = CameraModule()
            self.camera_module.photo_captured.connect(self.on_photo_captured)
            layout.addWidget(self.camera_module)
        else:
            no_camera_label = QLabel("摄像头模块不可用")
            no_camera_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(no_camera_label)

        # 已拍照片列表
        photos_frame = QFrame()
        photos_frame.setObjectName("photosFrame")
        photos_layout = QVBoxLayout(photos_frame)

        photos_title = QLabel("📸 已拍照片")
        photos_title.setFont(QFont("Inter", 14, QFont.Weight.Bold))
        photos_layout.addWidget(photos_title)

        self.photos_list = QTextEdit()
        self.photos_list.setMaximumHeight(100)
        self.photos_list.setReadOnly(True)
        photos_layout.addWidget(self.photos_list)

        # 清空照片按钮
        clear_photos_btn = QPushButton("🗑️ 清空照片")
        clear_photos_btn.setObjectName("clearPhotosBtn")
        clear_photos_btn.clicked.connect(self.clear_photos)
        photos_layout.addWidget(clear_photos_btn)

        layout.addWidget(photos_frame)

        return widget

    def create_bottom_actions(self, parent_layout):
        """创建底部操作区域"""
        actions_frame = QFrame()
        actions_frame.setObjectName("actionsFrame")
        actions_layout = QHBoxLayout(actions_frame)

        # 重置按钮
        reset_btn = QPushButton("🔄 重置")
        reset_btn.setObjectName("resetBtn")
        reset_btn.clicked.connect(self.reset_form)

        # 预览按钮
        preview_btn = QPushButton("👁️ 预览")
        preview_btn.setObjectName("previewBtn")
        preview_btn.clicked.connect(self.preview_submission)

        # 提交按钮
        submit_btn = QPushButton("📤 提交出库")
        submit_btn.setObjectName("submitBtn")
        submit_btn.clicked.connect(self.submit_stock_out)

        actions_layout.addWidget(reset_btn)
        actions_layout.addStretch()
        actions_layout.addWidget(preview_btn)
        actions_layout.addWidget(submit_btn)

        parent_layout.addWidget(actions_frame)

    def apply_styles(self):
        """应用样式"""
        # 主容器样式
        self.setStyleSheet("""
            QWidget {
                background: transparent;
                color: white;
                font-family: 'Inter', 'Segoe UI', sans-serif;
            }

            /* 标题样式 */
            QLabel#titleLabel {
                color: white;
                font-size: 24px;
                font-weight: 700;
                background: transparent;
                border: none;
            }

            QLabel#statusLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 14px;
                background: transparent;
                border: none;
            }

            /* 框架样式 */
            QFrame#titleFrame, QFrame#selectionFrame, QFrame#operationFrame {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 16px;
                padding: 15px;
            }

            QFrame#statusFrame, QFrame#weightFrame, QFrame#manualFrame, QFrame#photosFrame, QFrame#actionsFrame {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 12px;
                padding: 10px;
            }

            /* 分组框样式 */
            QGroupBox {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 12px;
                padding-top: 15px;
                font-weight: 600;
                color: white;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)

        # 应用按钮样式
        self.apply_button_styles()

        # 应用输入控件样式
        self.apply_input_styles()

        # 应用表格样式
        self.apply_table_styles()

    def apply_button_styles(self):
        """应用按钮样式"""
        button_style = GlassmorphismStyles.get_button_style()

        # 为所有按钮应用样式
        for btn in self.findChildren(QPushButton):
            btn.setStyleSheet(button_style)

    def apply_input_styles(self):
        """应用输入控件样式"""
        input_style = """
            QLineEdit, QComboBox, QDateEdit, QSpinBox {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                padding: 8px 12px;
                color: white;
                font-size: 14px;
                min-height: 20px;
            }

            QLineEdit:focus, QComboBox:focus, QDateEdit:focus, QSpinBox:focus {
                border: 1px solid rgba(147, 51, 234, 0.5);
                background: rgba(255, 255, 255, 0.15);
            }

            QComboBox {
                min-width: 200px;
                padding-right: 25px;
            }

            QComboBox::drop-down {
                border: none;
                width: 25px;
                background: rgba(255, 255, 255, 0.1);
                border-top-right-radius: 8px;
                border-bottom-right-radius: 8px;
            }

            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid white;
                margin-right: 5px;
            }

            QComboBox QAbstractItemView {
                background: rgba(40, 40, 40, 0.95);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                color: white;
                selection-background-color: rgba(147, 51, 234, 0.3);
                padding: 4px;
            }

            QComboBox QAbstractItemView::item {
                padding: 8px 12px;
                border: none;
                color: white;
                min-height: 20px;
            }

            QComboBox QAbstractItemView::item:selected {
                background: rgba(147, 51, 234, 0.3);
                color: white;
            }

            QComboBox QAbstractItemView::item:hover {
                background: rgba(255, 255, 255, 0.1);
                color: white;
            }

            QDateEdit::drop-down {
                border: none;
                width: 20px;
            }

            QSpinBox::up-button, QSpinBox::down-button {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                width: 16px;
            }

            QSpinBox::up-arrow, QSpinBox::down-arrow {
                width: 8px;
                height: 8px;
            }
        """

        for widget in self.findChildren((QLineEdit, QComboBox, QDateEdit, QSpinBox)):
            widget.setStyleSheet(input_style)

    def apply_table_styles(self):
        """应用表格样式"""
        table_style = """
            QTableWidget {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                gridline-color: rgba(255, 255, 255, 0.1);
                color: white;
                selection-background-color: rgba(147, 51, 234, 0.3);
                font-size: 13px;
                alternate-background-color: rgba(255, 255, 255, 0.02);
            }

            QTableWidget::item {
                padding: 10px 8px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                color: white;
                text-align: left;
            }

            QTableWidget::item:selected {
                background: rgba(147, 51, 234, 0.3);
                color: white;
            }

            QTableWidget::item:hover {
                background: rgba(255, 255, 255, 0.1);
            }

            QHeaderView::section {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                padding: 10px 8px;
                color: white;
                font-weight: 600;
                font-size: 13px;
                text-align: left;
            }

            QHeaderView::section:hover {
                background: rgba(255, 255, 255, 0.15);
            }
        """

        for table in self.findChildren(QTableWidget):
            table.setStyleSheet(table_style)

    def setup_connections(self):
        """设置信号连接"""
        # 下拉框变化
        self.depot_combo.currentTextChanged.connect(self.on_depot_changed)
        self.project_combo.currentTextChanged.connect(self.on_project_changed)
        self.meal_time_combo.currentTextChanged.connect(self.on_meal_time_changed)

        # 搜索框
        self.search_edit.textChanged.connect(self.filter_stock_list)

        # 表格选择
        self.stock_table.itemSelectionChanged.connect(self.on_stock_selection_changed)

    def set_api(self, api):
        """设置API实例"""
        self.api = api
        # 自动加载数据
        QTimer.singleShot(500, self.load_all_data)

    def load_all_data(self):
        """加载所有基础数据"""
        if not self.api:
            QMessageBox.warning(self, "警告", "API未初始化，请先登录")
            return

        self.status_label.setText("正在加载数据...")
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # 加载仓库数据
        self.load_data("depot")

    def load_data(self, data_type: str):
        """加载指定类型的数据"""
        if data_type in self.loader_threads and self.loader_threads[data_type].isRunning():
            return

        thread = StockDataLoader(self.api, data_type)
        thread.data_loaded.connect(self.on_data_loaded)
        thread.error_occurred.connect(self.on_data_error)
        thread.progress_updated.connect(self.progress_bar.setValue)
        thread.finished.connect(lambda: self.on_load_finished(data_type))

        self.loader_threads[data_type] = thread
        thread.start()

    def on_data_loaded(self, data_type: str, result: dict):
        """数据加载完成"""
        data = result.get('data', [])

        if data_type == "depot":
            self.depot_data = data
            self.update_depot_combo()
            # 继续加载项目数据
            QTimer.singleShot(100, lambda: self.load_data("project"))

        elif data_type == "project":
            self.project_data = data
            self.update_project_combo()
            # 继续加载餐别数据
            QTimer.singleShot(100, lambda: self.load_data("meal_time"))

        elif data_type == "meal_time":
            self.meal_time_data = data
            self.update_meal_time_combo()
            # 继续加载库存数据
            QTimer.singleShot(100, lambda: self.load_data("stock"))

        elif data_type == "stock":
            self.stock_data = data
            self.update_stock_table()

    def on_data_error(self, error_msg: str):
        """数据加载错误"""
        QMessageBox.critical(self, "错误", error_msg)
        self.status_label.setText("数据加载失败")

    def on_load_finished(self, data_type: str):
        """加载完成"""
        if data_type == "stock":
            self.progress_bar.setVisible(False)
            self.status_label.setText("数据加载完成")

    def update_depot_combo(self):
        """更新仓库下拉框"""
        self.depot_combo.clear()
        self.depot_combo.addItem("请选择仓库", "")

        for depot in self.depot_data:
            self.depot_combo.addItem(f"{depot.get('name', '')} ({depot.get('code', '')})", depot.get('code', ''))

    def update_project_combo(self):
        """更新项目下拉框"""
        self.project_combo.clear()
        self.project_combo.addItem("请选择项目", "")

        for project in self.project_data:
            self.project_combo.addItem(f"{project.get('name', '')} ({project.get('code', '')})", project.get('code', ''))

    def update_meal_time_combo(self):
        """更新餐别下拉框"""
        self.meal_time_combo.clear()
        self.meal_time_combo.addItem("请选择餐别", "")

        for meal_time in self.meal_time_data:
            self.meal_time_combo.addItem(meal_time.get('name', ''), meal_time.get('value', ''))

    def update_stock_table(self):
        """更新库存表格"""
        self.stock_table.setRowCount(len(self.stock_data))

        for row, stock in enumerate(self.stock_data):
            self.stock_table.setItem(row, 0, QTableWidgetItem(stock.get('code', '')))
            self.stock_table.setItem(row, 1, QTableWidgetItem(stock.get('name', '')))
            self.stock_table.setItem(row, 2, QTableWidgetItem(stock.get('unit', '')))
            self.stock_table.setItem(row, 3, QTableWidgetItem(str(stock.get('quantity', 0))))

    def filter_stock_list(self):
        """过滤库存列表"""
        search_text = self.search_edit.text().lower()

        for row in range(self.stock_table.rowCount()):
            show_row = False

            # 检查商品代码和名称
            for col in [0, 1]:  # 代码和名称列
                item = self.stock_table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break

            self.stock_table.setRowHidden(row, not show_row)

    def on_depot_changed(self):
        """仓库选择变化"""
        depot_code = self.depot_combo.currentData()
        if depot_code:
            self.status_label.setText(f"已选择仓库: {self.depot_combo.currentText()}")

    def on_project_changed(self):
        """项目选择变化"""
        project_code = self.project_combo.currentData()
        if project_code:
            self.status_label.setText(f"已选择项目: {self.project_combo.currentText()}")

    def on_meal_time_changed(self):
        """餐别选择变化"""
        meal_time_value = self.meal_time_combo.currentData()
        if meal_time_value:
            self.status_label.setText(f"已选择餐别: {self.meal_time_combo.currentText()}")

    def on_stock_selection_changed(self):
        """库存选择变化"""
        current_row = self.stock_table.currentRow()
        if current_row >= 0:
            stock_name = self.stock_table.item(current_row, 1).text()
            self.status_label.setText(f"已选择商品: {stock_name}")

    def add_selected_stock(self):
        """添加选中的库存商品"""
        current_row = self.stock_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请先选择要出库的商品")
            return

        # 获取选中商品信息
        stock_code = self.stock_table.item(current_row, 0).text()
        stock_name = self.stock_table.item(current_row, 1).text()
        stock_unit = self.stock_table.item(current_row, 2).text()

        # 检查是否已经添加
        for item in self.selected_stock_items:
            if item['code'] == stock_code:
                QMessageBox.information(self, "提示", "该商品已经添加到出库列表")
                return

        # 添加到选中列表
        stock_item = {
            'code': stock_code,
            'name': stock_name,
            'unit': stock_unit,
            'quantity': self.current_weight,
            'photos': self.captured_photos.copy()
        }

        self.selected_stock_items.append(stock_item)
        self.update_selected_table()

        QMessageBox.information(self, "成功", f"已添加商品: {stock_name}")

    def update_selected_table(self):
        """更新选中商品表格"""
        self.selected_table.setRowCount(len(self.selected_stock_items))

        for row, item in enumerate(self.selected_stock_items):
            self.selected_table.setItem(row, 0, QTableWidgetItem(item['code']))
            self.selected_table.setItem(row, 1, QTableWidgetItem(item['name']))
            self.selected_table.setItem(row, 2, QTableWidgetItem(f"{item['quantity']} kg"))
            self.selected_table.setItem(row, 3, QTableWidgetItem(item['unit']))

            # 删除按钮
            remove_btn = QPushButton("🗑️ 删除")
            remove_btn.setObjectName("removeBtn")
            remove_btn.clicked.connect(lambda: self.remove_selected_item(row))
            self.selected_table.setCellWidget(row, 4, remove_btn)

    def remove_selected_item(self, row: int):
        """删除选中的商品"""
        if 0 <= row < len(self.selected_stock_items):
            item_name = self.selected_stock_items[row]['name']
            self.selected_stock_items.pop(row)
            self.update_selected_table()
            self.status_label.setText(f"已删除商品: {item_name}")

    def setup_serial_connection(self):
        """设置串口连接"""
        # 初始化串口状态
        self.serial_worker = None
        self.serial_status_label.setText("串口状态: 未连接")
        self.connect_btn.setText("🔌 连接串口")
        self.status_label.setText("点击连接按钮启动串口监听")
        print("✅ 出库管理模块串口连接已初始化")

    def smart_serial_connection(self):
        """智能串口连接 - 检测端口占用情况"""
        try:
            print("🔍 出库管理模块检查串口状态...")

            # 检查是否有其他模块正在使用串口
            main_window = self.get_main_window()
            if main_window and hasattr(main_window, 'order_page'):
                order_page = main_window.order_page
                # 检查订单页面是否有活跃的串口连接
                if hasattr(order_page, 'serial_worker') and order_page.serial_worker and order_page.serial_worker.running:
                    print("🔴 检测到订单管理模块正在使用串口")
                    # 显示提示信息，不自动连接
                    self.serial_status_label.setText("串口状态: 被其他模块占用")
                    self.status_label.setText("串口正被订单管理模块使用，请先断开该连接")
                    return
                else:
                    print("✅ 订单管理模块未使用串口")

            # 检查串口配置
            try:
                from config.settings import settings
                port = settings.serial_port
                print(f"📋 串口配置: port={port}, baudrate={settings.serial_baudrate}")
            except Exception as e:
                print(f"❌ 读取串口配置失败: {e}")

            # 如果没有冲突，显示可连接状态
            self.serial_status_label.setText("串口状态: 未连接")
            self.status_label.setText("点击连接按钮启动串口监听")

        except Exception as e:
            print(f"❌ 串口状态检测异常: {e}")
            self.serial_status_label.setText("串口状态: 检测失败")
            self.status_label.setText(f"串口状态检测失败: {str(e)}")

    def get_main_window(self):
        """获取主窗口实例"""
        widget = self
        while widget.parent():
            widget = widget.parent()
            if hasattr(widget, 'order_page'):
                return widget
        return None

    def try_connect_to_existing_serial(self):
        """尝试连接到已有的串口实例"""
        try:
            # 尝试从主窗口获取已有的串口实例
            main_window = self.get_main_window()
            if main_window and hasattr(main_window, 'order_page'):
                order_page = main_window.order_page
                # 检查订单页面是否有活跃的串口连接
                if hasattr(order_page, 'serial_worker') and order_page.serial_worker and order_page.serial_worker.running:
                    # 连接到已有的串口信号
                    order_page.serial_worker.weight_received.connect(self.on_weight_received)
                    self.serial_status_label.setText("串口状态: 已连接 (共享)")
                    self.connect_btn.setText("🔌 断开串口")
                    self.status_label.setText("已连接到共享串口，等待重量数据...")
                    return True

            # 如果没有找到活跃的串口，显示提示
            self.serial_status_label.setText("串口状态: 未连接")
            self.status_label.setText("请先在订单管理页面连接串口")
            return False

        except Exception as e:
            self.serial_status_label.setText("串口状态: 连接失败")
            self.status_label.setText(f"串口连接失败: {str(e)}")
            return False

    def get_main_window(self):
        """获取主窗口实例"""
        widget = self
        while widget.parent():
            widget = widget.parent()
            if hasattr(widget, 'order_page'):
                return widget
        return None

    def start_serial_monitoring(self):
        """启动串口监听（独立模式，可能导致端口冲突）"""
        if not SerialWorker:
            self.serial_status_label.setText("串口状态: 模块不可用")
            return

        # 警告用户可能的端口冲突
        reply = QMessageBox.question(
            self,
            "串口连接确认",
            "检测到可能存在串口冲突。\n\n"
            "建议：\n"
            "1. 先在订单管理页面连接串口\n"
            "2. 然后在此页面使用共享连接\n\n"
            "是否仍要独立连接串口？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply != QMessageBox.StandardButton.Yes:
            return

        try:
            # 从配置文件获取串口设置
            from config.settings import settings

            port = settings.serial_port
            baudrate = settings.serial_baudrate
            timeout = settings.serial_timeout
            auto_detect = settings.serial_auto_detect

            self.serial_worker = SerialWorker(port, baudrate, timeout, auto_detect)
            self.serial_worker.weight_received.connect(self.on_weight_received)
            self.serial_worker.error_occurred.connect(self.on_serial_error)
            self.serial_worker.connection_status.connect(self.on_serial_status_changed)
            self.serial_worker.port_detected.connect(self.on_port_detected)
            self.serial_worker.start()

            if auto_detect:
                self.serial_status_label.setText("正在检测串口...")
            else:
                self.serial_status_label.setText(f"正在连接串口 {port}...")

        except Exception as e:
            self.on_serial_error(f"启动串口监听失败: {str(e)}")

    def toggle_serial_connection(self):
        """切换串口连接"""
        print(f"🔍 toggle_serial_connection 被调用")

        if self.serial_worker and self.serial_worker.running:
            # 断开连接
            self.serial_worker.stop()
            self.serial_worker = None
            self.serial_status_label.setText("串口状态: 未连接")
            self.connect_btn.setText("🔌 连接串口")
            self.status_label.setText("串口已断开")
            print("🔌 出库管理模块断开串口连接")
        else:
            # 检查端口冲突
            if self.check_port_conflict():
                return

            # 启动连接
            print("🚀 出库管理模块启动串口连接")
            self.start_serial_monitoring_with_conflict_check()



    def check_port_conflict(self):
        """检查端口冲突"""
        try:
            main_window = self.get_main_window()
            if main_window and hasattr(main_window, 'order_page'):
                order_page = main_window.order_page
                if hasattr(order_page, 'serial_worker') and order_page.serial_worker and order_page.serial_worker.running:
                    reply = QMessageBox.question(
                        self,
                        "端口冲突检测",
                        "检测到订单管理模块正在使用串口。\n\n"
                        "建议操作：\n"
                        "1. 先在订单管理页面断开串口\n"
                        "2. 然后在此页面重新连接\n\n"
                        "或者：\n"
                        "- 直接在订单管理页面查看重量数据\n\n"
                        "是否仍要强制连接？（可能导致连接失败）",
                        QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                        QMessageBox.StandardButton.No
                    )
                    return reply != QMessageBox.StandardButton.Yes
            return False
        except Exception:
            return False

    def start_serial_monitoring_with_conflict_check(self):
        """带冲突检查的串口监听启动"""
        try:
            # 从配置文件获取串口设置
            from config.settings import settings

            port = settings.serial_port
            baudrate = settings.serial_baudrate
            timeout = settings.serial_timeout
            auto_detect = settings.serial_auto_detect

            # 先检查端口是否可用
            if not auto_detect and not self.check_port_available(port):
                self.on_serial_error(f"端口 {port} 不可用或被占用")
                return

            print(f"🔍 出库管理尝试连接串口: port={port}, baudrate={baudrate}, auto_detect={auto_detect}")

            self.serial_worker = SerialWorker(port, baudrate, timeout, auto_detect)
            self.serial_worker.weight_received.connect(self.on_weight_received)
            self.serial_worker.error_occurred.connect(self.on_serial_error)
            self.serial_worker.connection_status.connect(self.on_serial_status_changed)
            self.serial_worker.port_detected.connect(self.on_port_detected)
            self.serial_worker.start()

            if auto_detect:
                self.serial_status_label.setText("正在检测串口...")
                self.status_label.setText("正在自动检测重量秤设备...")
            else:
                self.serial_status_label.setText(f"正在连接串口 {port}...")
                self.status_label.setText(f"正在连接串口 {port}...")

        except Exception as e:
            self.on_serial_error(f"启动串口监听失败: {str(e)}")

    def check_port_available(self, port):
        """检查端口是否可用"""
        try:
            import serial
            # 尝试短暂连接测试端口可用性
            test_serial = serial.Serial(port, 9600, timeout=0.1)
            test_serial.close()
            return True
        except Exception as e:
            print(f"🔴 端口 {port} 不可用: {e}")
            return False

    def on_weight_received(self, weight: str):
        """接收到重量数据"""
        try:
            # 解析重量值
            weight_value = float(weight.replace('kg', '').strip())
            self.current_weight = f"{weight_value:.2f}"
            self.weight_display.setText(f"{self.current_weight} kg")
            self.status_label.setText(f"重量更新: {self.current_weight} kg")
        except ValueError:
            pass

    def on_serial_status_changed(self, connected: bool):
        """串口连接状态变化"""
        if connected:
            # 获取当前端口信息
            port_info = ""
            if self.serial_worker and hasattr(self.serial_worker, 'port'):
                port_info = f" ({self.serial_worker.port})"

            self.serial_status_label.setText(f"串口状态: 已连接{port_info}")
            self.connect_btn.setText("🔌 断开串口")
            self.status_label.setText("串口连接成功，等待重量数据...")
        else:
            self.serial_status_label.setText("串口状态: 未连接")
            self.connect_btn.setText("🔌 连接串口")
            self.status_label.setText("串口已断开")

    def on_serial_error(self, error: str):
        """串口错误"""
        print(f"🔴 出库管理串口错误: {error}")  # 输出到控制台便于调试

        # 检查是否是端口占用错误
        if "PermissionError" in error or "拒绝访问" in error or "could not open port" in error:
            self.serial_status_label.setText("串口状态: 端口被占用")
            self.status_label.setText("串口被其他程序占用，请先断开订单管理的串口连接")
        else:
            self.serial_status_label.setText("串口状态: 错误")
            self.status_label.setText(f"串口错误: {error}")

        # 不弹出对话框，避免阻塞界面
        # QMessageBox.warning(self, "串口错误", error)

    def on_port_detected(self, port: str):
        """检测到串口"""
        self.serial_status_label.setText(f"检测到串口: {port}")
        self.status_label.setText(f"检测到重量秤设备在端口: {port}")

    def cleanup_serial_connection(self):
        """清理串口连接"""
        if self.serial_worker:
            self.serial_worker.stop()
            self.serial_worker = None
            print("🗑️ 出库管理模块串口连接已清理")

    def closeEvent(self, event):
        """窗口关闭事件"""
        self.cleanup_serial_connection()
        super().closeEvent(event)

    def set_manual_weight(self):
        """设置手动输入的重量"""
        try:
            weight_text = self.manual_weight_edit.text().strip()
            weight_value = float(weight_text)
            self.current_weight = f"{weight_value:.2f}"
            self.weight_display.setText(f"{self.current_weight} kg")
            self.status_label.setText(f"手动设置重量: {self.current_weight} kg")
            self.manual_weight_edit.clear()
        except ValueError:
            QMessageBox.warning(self, "错误", "请输入有效的重量数值")

    def on_photo_captured(self, photo_path: str):
        """照片拍摄完成"""
        self.captured_photos.append(photo_path)
        self.update_photos_list()
        self.status_label.setText(f"已拍摄 {len(self.captured_photos)} 张照片")

    def update_photos_list(self):
        """更新照片列表显示"""
        photos_text = "\n".join([f"📷 {os.path.basename(path)}" for path in self.captured_photos])
        self.photos_list.setText(photos_text)

    def clear_photos(self):
        """清空照片"""
        self.captured_photos.clear()
        self.photos_list.clear()
        self.status_label.setText("已清空照片")

    def reset_form(self):
        """重置表单"""
        # 重置选择
        self.depot_combo.setCurrentIndex(0)
        self.project_combo.setCurrentIndex(0)
        self.meal_time_combo.setCurrentIndex(0)
        self.people_spin.setValue(100)
        self.date_edit.setDate(QDate.currentDate())

        # 清空选中商品
        self.selected_stock_items.clear()
        self.update_selected_table()

        # 重置重量和照片
        self.current_weight = "0.0"
        self.weight_display.setText("0.0 kg")
        self.manual_weight_edit.clear()
        self.clear_photos()

        # 清空搜索
        self.search_edit.clear()

        self.status_label.setText("表单已重置")

    def preview_submission(self):
        """预览提交数据"""
        if not self.validate_form():
            return

        # 构建预览数据
        preview_data = self.build_submission_data()

        # 显示预览对话框
        preview_text = self.format_preview_data(preview_data)

        msg = QMessageBox()
        msg.setWindowTitle("出库预览")
        msg.setText("请确认以下出库信息:")
        msg.setDetailedText(preview_text)
        msg.setStandardButtons(QMessageBox.StandardButton.Ok)
        msg.exec()

    def validate_form(self) -> bool:
        """验证表单数据"""
        # 检查基础选择
        if not self.depot_combo.currentData():
            QMessageBox.warning(self, "验证失败", "请选择仓库")
            return False

        if not self.project_combo.currentData():
            QMessageBox.warning(self, "验证失败", "请选择项目")
            return False

        if not self.meal_time_combo.currentData():
            QMessageBox.warning(self, "验证失败", "请选择餐别")
            return False

        # 检查选中商品
        if not self.selected_stock_items:
            QMessageBox.warning(self, "验证失败", "请至少选择一个商品进行出库")
            return False

        # 检查重量和照片
        for item in self.selected_stock_items:
            if float(item['quantity']) <= 0:
                QMessageBox.warning(self, "验证失败", f"商品 {item['name']} 的重量必须大于0")
                return False

            if not item['photos']:
                QMessageBox.warning(self, "验证失败", f"商品 {item['name']} 必须拍摄照片")
                return False

        return True

    def build_submission_data(self) -> Dict[str, Any]:
        """构建提交数据"""
        # 构建出库明细
        details = []
        for item in self.selected_stock_items:
            detail = self.api.create_stock_detail(
                code=item['code'],
                quantity=item['quantity'],
                unit=item['unit'],
                path=";".join(item['photos']) + ";"
            )
            details.append(detail)

        return {
            'datetime': self.date_edit.date().toString("yyyy-MM-dd"),
            'depot_code': self.depot_combo.currentData(),
            'project_code': self.project_combo.currentData(),
            'time_type': int(self.meal_time_combo.currentData()),
            'people': self.people_spin.value(),
            'details': details
        }

    def format_preview_data(self, data: Dict[str, Any]) -> str:
        """格式化预览数据"""
        lines = [
            f"出库日期: {data['datetime']}",
            f"仓库: {self.depot_combo.currentText()}",
            f"项目: {self.project_combo.currentText()}",
            f"餐别: {self.meal_time_combo.currentText()}",
            f"人数: {data['people']}",
            "",
            "出库明细:",
        ]

        for i, detail in enumerate(data['details'], 1):
            lines.append(f"  {i}. 商品代码: {detail['code']}")
            lines.append(f"     出库数量: {detail['quantity']} {detail['unit']}")
            lines.append(f"     照片数量: {len(detail['path'].split(';')) - 1}")
            lines.append("")

        return "\n".join(lines)

    def submit_stock_out(self):
        """提交出库申请"""
        if not self.api:
            QMessageBox.warning(self, "错误", "API未初始化")
            return

        if not self.validate_form():
            return

        # 确认提交
        reply = QMessageBox.question(
            self, "确认提交",
            "确定要提交出库申请吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply != QMessageBox.StandardButton.Yes:
            return

        # 构建提交数据
        submit_data = self.build_submission_data()

        # 显示进度
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("正在提交出库申请...")

        # 启动提交线程
        self.submit_thread = StockOutSubmitter(self.api, submit_data)
        self.submit_thread.submit_completed.connect(self.on_submit_completed)
        self.submit_thread.error_occurred.connect(self.on_submit_error)
        self.submit_thread.progress_updated.connect(self.progress_bar.setValue)
        self.submit_thread.finished.connect(self.on_submit_finished)
        self.submit_thread.start()

    def on_submit_completed(self, result: dict):
        """提交完成"""
        order_no = result.get('msg', '未知')
        QMessageBox.information(self, "提交成功", f"出库申请提交成功！\n出库单号: {order_no}")

        # 重置表单
        self.reset_form()

    def on_submit_error(self, error: str):
        """提交错误"""
        QMessageBox.critical(self, "提交失败", f"出库申请提交失败:\n{error}")

    def on_submit_finished(self):
        """提交完成（无论成功失败）"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("就绪")

    def closeEvent(self, event):
        """关闭事件"""
        # 停止串口连接
        if self.serial_worker and self.serial_worker.running:
            self.serial_worker.stop()

        # 停止摄像头
        if self.camera_module:
            self.camera_module.stop_camera()

        event.accept()
