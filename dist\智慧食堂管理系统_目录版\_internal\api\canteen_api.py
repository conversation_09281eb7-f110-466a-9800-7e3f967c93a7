#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
食堂管理API接口
Canteen Management API
"""

from typing import Dict, Any, List, Optional
from .auth_api import AuthAPI

class CanteenAPI(AuthAPI):
    """食堂管理API类"""
    
    def __init__(self, base_url: str):
        """
        初始化食堂管理API
        
        Args:
            base_url: API基础URL
        """
        super().__init__(base_url)
    
    def get_warehouse_list(self) -> Dict[str, Any]:
        """
        获取仓库列表
        
        Returns:
            仓库列表响应
        """
        return self.make_authenticated_request('GET', '?op=warehouse_list')
    
    def get_project_list(self) -> Dict[str, Any]:
        """
        获取项目列表
        
        Returns:
            项目列表响应
        """
        return self.make_authenticated_request('GET', '?op=project_list')
    
    def get_meal_list(self) -> Dict[str, Any]:
        """
        获取餐别列表

        Returns:
            餐别列表响应
        """
        return self.make_authenticated_request('GET', '?op=meal_list')

    def get_orders(self, day: Optional[str] = None) -> Dict[str, Any]:
        """
        获取订单列表

        Args:
            day: 可选的日期参数，格式为 YYYY-MM-DD，如 "2023-06-08"

        Returns:
            订单列表响应，包含以下字段：
            - code: 响应状态码
            - msg: 响应消息
            - data: 订单数据列表
            - total: 订单总数
        """
        # 准备请求数据
        data = {}
        if day:
            data['day'] = day

        # 发起POST请求
        return self.make_authenticated_request('POST', '?op=order', data=data)
    
    def get_inventory_list(self, warehouse_id: Optional[str] = None) -> Dict[str, Any]:
        """
        获取库存列表
        
        Args:
            warehouse_id: 仓库ID（可选）
            
        Returns:
            库存列表响应
        """
        params = {}
        if warehouse_id:
            params['warehouse_id'] = warehouse_id
        
        return self.make_authenticated_request('GET', '?op=inventory_list', params=params)
    
    def submit_outbound(self, outbound_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        提交出库
        
        Args:
            outbound_data: 出库数据
            
        Returns:
            出库提交响应
        """
        return self.make_authenticated_request('POST', '?op=outbound_submit', data=outbound_data)
    
    def register_inbound(self, inbound_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        入库登记
        
        Args:
            inbound_data: 入库数据
            
        Returns:
            入库登记响应
        """
        return self.make_authenticated_request('POST', '?op=inbound_register', data=inbound_data)
    
    def get_print_records(self, start_date: Optional[str] = None, end_date: Optional[str] = None) -> Dict[str, Any]:
        """
        获取打印记录
        
        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            
        Returns:
            打印记录响应
        """
        params = {}
        if start_date:
            params['start_date'] = start_date
        if end_date:
            params['end_date'] = end_date
        
        return self.make_authenticated_request('GET', '?op=print_records', params=params)
    
    def upload_picture(self, image_file, description: Optional[str] = None) -> Dict[str, Any]:
        """
        上传图片
        
        Args:
            image_file: 图片文件
            description: 图片描述
            
        Returns:
            图片上传响应
        """
        files = {'image': image_file}
        data = {}
        if description:
            data['description'] = description
        
        return self.make_authenticated_request('POST', '?op=picture', files=files, data=data)
    
    def get_recipe_list(self, date: Optional[str] = None) -> Dict[str, Any]:
        """
        获取食谱列表
        
        Args:
            date: 日期 (YYYY-MM-DD)
            
        Returns:
            食谱列表响应
        """
        params = {}
        if date:
            params['date'] = date
        
        return self.make_authenticated_request('GET', '?op=recipe_list', params=params)
    
    def create_recipe(self, recipe_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建食谱
        
        Args:
            recipe_data: 食谱数据
            
        Returns:
            创建食谱响应
        """
        return self.make_authenticated_request('POST', '?op=recipe_create', data=recipe_data)
    
    def update_recipe(self, recipe_id: str, recipe_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新食谱
        
        Args:
            recipe_id: 食谱ID
            recipe_data: 食谱数据
            
        Returns:
            更新食谱响应
        """
        recipe_data['recipe_id'] = recipe_id
        return self.make_authenticated_request('POST', '?op=recipe_update', data=recipe_data)
    
    def delete_recipe(self, recipe_id: str) -> Dict[str, Any]:
        """
        删除食谱
        
        Args:
            recipe_id: 食谱ID
            
        Returns:
            删除食谱响应
        """
        return self.make_authenticated_request('POST', '?op=recipe_delete', data={'recipe_id': recipe_id})
    
    def get_purchase_orders(self, status: Optional[str] = None) -> Dict[str, Any]:
        """
        获取采购订单列表
        
        Args:
            status: 订单状态
            
        Returns:
            采购订单列表响应
        """
        params = {}
        if status:
            params['status'] = status
        
        return self.make_authenticated_request('GET', '?op=purchase_orders', params=params)
    
    def create_purchase_order(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建采购订单
        
        Args:
            order_data: 订单数据
            
        Returns:
            创建订单响应
        """
        return self.make_authenticated_request('POST', '?op=purchase_order_create', data=order_data)
    
    def update_order_status(self, order_id: str, status: str) -> Dict[str, Any]:
        """
        更新订单状态
        
        Args:
            order_id: 订单ID
            status: 新状态
            
        Returns:
            更新状态响应
        """
        return self.make_authenticated_request('POST', '?op=order_status_update', 
                                             data={'order_id': order_id, 'status': status})
    
    def get_reports(self, report_type: str, start_date: str, end_date: str) -> Dict[str, Any]:
        """
        获取报表数据
        
        Args:
            report_type: 报表类型
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            报表数据响应
        """
        params = {
            'report_type': report_type,
            'start_date': start_date,
            'end_date': end_date
        }
        
        return self.make_authenticated_request('GET', '?op=reports', params=params)
