#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
认证管理器
Authentication Manager
"""

import os
import json
import time
import hashlib
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta

class AuthManager:
    """认证管理器类"""
    
    def __init__(self, storage_file: str = "config/auth_storage.json"):
        """
        初始化认证管理器
        
        Args:
            storage_file: 存储文件路径
        """
        self.storage_file = storage_file
        self.current_token = None
        self.current_user = None
        self.token_expire_time = None
        
        # 确保存储目录存在
        os.makedirs(os.path.dirname(storage_file), exist_ok=True)
        
        # 加载存储的认证信息
        self.load_auth_data()
    
    def load_auth_data(self):
        """加载认证数据"""
        try:
            if os.path.exists(self.storage_file):
                with open(self.storage_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                    # 加载当前token信息
                    current_auth = data.get('current_auth', {})
                    if current_auth:
                        self.current_token = current_auth.get('token')
                        self.current_user = current_auth.get('username')
                        expire_timestamp = current_auth.get('expire_time')
                        if expire_timestamp:
                            self.token_expire_time = datetime.fromtimestamp(expire_timestamp)
                    
        except Exception as e:
            print(f"加载认证数据失败: {e}")
    
    def save_auth_data(self):
        """保存认证数据"""
        try:
            # 读取现有数据
            data = {}
            if os.path.exists(self.storage_file):
                with open(self.storage_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            
            # 更新当前认证信息
            if self.current_token and self.current_user:
                data['current_auth'] = {
                    'token': self.current_token,
                    'username': self.current_user,
                    'expire_time': self.token_expire_time.timestamp() if self.token_expire_time else None,
                    'login_time': time.time()
                }
            else:
                data['current_auth'] = {}
            
            # 保存到文件
            with open(self.storage_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存认证数据失败: {e}")
    
    def save_login_success(self, username: str, token: str, expire_hours: int = 24):
        """
        保存登录成功的信息
        
        Args:
            username: 用户名
            token: 访问令牌
            expire_hours: token过期时间（小时）
        """
        self.current_user = username
        self.current_token = token
        self.token_expire_time = datetime.now() + timedelta(hours=expire_hours)
        
        # 保存到历史记录
        self.add_to_history(username)
        
        # 保存认证数据
        self.save_auth_data()
    
    def add_to_history(self, username: str):
        """
        添加用户到历史记录
        
        Args:
            username: 用户名
        """
        try:
            # 读取现有数据
            data = {}
            if os.path.exists(self.storage_file):
                with open(self.storage_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            
            # 获取历史记录
            history = data.get('login_history', [])
            
            # 移除重复的用户名
            history = [item for item in history if item.get('username') != username]
            
            # 添加新记录到开头
            history.insert(0, {
                'username': username,
                'last_login': time.time(),
                'login_count': self.get_login_count(username) + 1
            })
            
            # 限制历史记录数量（最多保存10个）
            history = history[:10]
            
            # 保存更新后的数据
            data['login_history'] = history
            with open(self.storage_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存历史记录失败: {e}")
    
    def get_login_count(self, username: str) -> int:
        """
        获取用户登录次数
        
        Args:
            username: 用户名
            
        Returns:
            登录次数
        """
        try:
            if os.path.exists(self.storage_file):
                with open(self.storage_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    history = data.get('login_history', [])
                    
                    for item in history:
                        if item.get('username') == username:
                            return item.get('login_count', 0)
        except:
            pass
        
        return 0
    
    def get_login_history(self) -> List[Dict]:
        """
        获取登录历史记录
        
        Returns:
            历史记录列表
        """
        try:
            if os.path.exists(self.storage_file):
                with open(self.storage_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('login_history', [])
        except:
            pass
        
        return []
    
    def is_token_valid(self) -> bool:
        """
        检查token是否有效
        
        Returns:
            是否有效
        """
        if not self.current_token or not self.token_expire_time:
            return False
        
        # 检查是否过期（提前5分钟判断过期）
        return datetime.now() < (self.token_expire_time - timedelta(minutes=5))
    
    def get_current_auth(self) -> Tuple[Optional[str], Optional[str]]:
        """
        获取当前认证信息
        
        Returns:
            (username, token) 元组
        """
        if self.is_token_valid():
            return self.current_user, self.current_token
        else:
            return None, None
    
    def clear_current_auth(self):
        """清除当前认证信息"""
        self.current_token = None
        self.current_user = None
        self.token_expire_time = None
        self.save_auth_data()
    
    def get_token_expire_info(self) -> Optional[str]:
        """
        获取token过期信息
        
        Returns:
            过期时间字符串
        """
        if not self.token_expire_time:
            return None
        
        now = datetime.now()
        if now >= self.token_expire_time:
            return "已过期"
        
        delta = self.token_expire_time - now
        hours = int(delta.total_seconds() // 3600)
        minutes = int((delta.total_seconds() % 3600) // 60)
        
        if hours > 0:
            return f"{hours}小时{minutes}分钟后过期"
        else:
            return f"{minutes}分钟后过期"
    
    def remove_from_history(self, username: str):
        """
        从历史记录中移除用户
        
        Args:
            username: 用户名
        """
        try:
            if os.path.exists(self.storage_file):
                with open(self.storage_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                history = data.get('login_history', [])
                history = [item for item in history if item.get('username') != username]
                
                data['login_history'] = history
                with open(self.storage_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                    
        except Exception as e:
            print(f"移除历史记录失败: {e}")

# 全局认证管理器实例
auth_manager = AuthManager()
