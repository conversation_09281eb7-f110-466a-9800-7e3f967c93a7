#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单管理模块
Order Management Module
"""

import sys
from datetime import datetime, date
from typing import Dict, Any, List, Optional

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
    QDateEdit, QComboBox, QTextEdit, QSplitter, QGroupBox,
    QHeaderView, QMessageBox, QProgressBar, QTabWidget,
    QScrollArea, QFrame
)
from PyQt6.QtCore import Qt, QDate, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QIcon

from api.order_api import OrderAPI
from config.settings import settings

class OrderLoadThread(QThread):
    """订单加载线程"""
    
    # 信号定义
    orders_loaded = pyqtSignal(dict)  # 订单加载完成
    error_occurred = pyqtSignal(str)  # 发生错误
    progress_updated = pyqtSignal(int)  # 进度更新
    
    def __init__(self, api: OrderAPI, day: Optional[str] = None):
        super().__init__()
        self.api = api
        self.day = day
    
    def run(self):
        """运行线程"""
        try:
            self.progress_updated.emit(30)
            
            # 调用API获取订单
            result = self.api.get_orders(self.day)
            
            self.progress_updated.emit(80)
            
            if result.get('code') == 200:
                self.orders_loaded.emit(result)
            else:
                self.error_occurred.emit(f"获取订单失败: {result.get('msg', '未知错误')}")
            
            self.progress_updated.emit(100)
            
        except Exception as e:
            self.error_occurred.emit(f"网络请求失败: {str(e)}")

class OrderModule(QWidget):
    """订单管理模块"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.api = None
        self.current_orders = []
        self.load_thread = None

        self.init_ui()
        self.setup_connections()

        # 延迟加载所有订单，避免在初始化时立即加载
        QTimer.singleShot(1000, self.auto_load_orders)
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 5, 10, 5)
        layout.setSpacing(5)

        # 设置Glassmorphism背景样式
        self.setStyleSheet("""
            QWidget {
                background: transparent;
                color: white;
                font-family: 'Inter', 'Segoe UI', sans-serif;
            }
        """)

        # 标题 - 进一步减小字体和间距
        title_label = QLabel("📦 订单管理")
        title_label.setFont(QFont("Inter", 16, QFont.Weight.Bold))
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: 600;
                margin: 0px;
                padding: 5px 0;
                background: transparent;
                border: none;
            }
        """)
        layout.addWidget(title_label)

        # 搜索和筛选区域 - 固定高度，不拉伸
        self.create_filter_section(layout)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 20px;
                height: 6px;
                text-align: center;
                color: white;
            }
            QProgressBar::chunk {
                background: linear-gradient(90deg, rgba(147, 51, 234, 0.8), rgba(59, 130, 246, 0.8));
                border-radius: 20px;
            }
        """)
        layout.addWidget(self.progress_bar)

        # 主要内容区域 - 使用stretch factor占用大部分空间
        self.create_main_content(layout)

        # 状态栏 - 固定高度，不拉伸
        self.create_status_bar(layout)
    
    def create_filter_section(self, parent_layout):
        """创建筛选区域"""
        filter_group = QGroupBox("筛选条件")
        filter_group.setStyleSheet("""
            QGroupBox {
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 12px;
                padding: 8px;
                font-size: 14px;
                font-weight: 600;
                color: white;
                margin: 0px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: white;
                font-size: 14px;
            }
        """)
        filter_layout = QGridLayout(filter_group)
        filter_layout.setSpacing(8)
        filter_layout.setContentsMargins(8, 15, 8, 8)

        # 创建标签样式
        label_style = """
            QLabel {
                color: rgba(255, 255, 255, 0.9);
                font-weight: 500;
                font-size: 14px;
                background: transparent;
                border: none;
            }
        """

        # 输入框样式
        input_style = """
            QLineEdit, QDateEdit {
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 12px;
                padding: 8px 12px;
                color: white;
                font-size: 13px;
                font-weight: 400;
                min-height: 20px;
            }
            QLineEdit:focus, QDateEdit:focus {
                border: 1px solid rgba(147, 51, 234, 0.5);
                box-shadow: 0 0 0 3px rgba(147, 51, 234, 0.1);
            }
            QLineEdit::placeholder {
                color: rgba(255, 255, 255, 0.6);
            }
            QDateEdit::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 20px;
                border-left: 1px solid rgba(255, 255, 255, 0.2);
                background: rgba(255, 255, 255, 0.1);
                border-top-right-radius: 12px;
                border-bottom-right-radius: 12px;
            }
            QDateEdit::down-arrow {
                image: none;
                border: none;
                width: 0px;
                height: 0px;
            }
        """

        # 日期选择
        date_label = QLabel("选择日期:")
        date_label.setStyleSheet(label_style)
        filter_layout.addWidget(date_label, 0, 0)

        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setStyleSheet(input_style)
        filter_layout.addWidget(self.date_edit, 0, 1)

        # 供应商搜索
        supplier_label = QLabel("供应商:")
        supplier_label.setStyleSheet(label_style)
        filter_layout.addWidget(supplier_label, 0, 2)

        self.supplier_edit = QLineEdit()
        self.supplier_edit.setPlaceholderText("输入供应商名称搜索...")
        self.supplier_edit.setStyleSheet(input_style)
        filter_layout.addWidget(self.supplier_edit, 0, 3)

        # 订单号搜索
        trade_no_label = QLabel("订单号:")
        trade_no_label.setStyleSheet(label_style)
        filter_layout.addWidget(trade_no_label, 1, 0)

        self.trade_no_edit = QLineEdit()
        self.trade_no_edit.setPlaceholderText("输入订单号搜索...")
        self.trade_no_edit.setStyleSheet(input_style)
        filter_layout.addWidget(self.trade_no_edit, 1, 1)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)

        # 按钮样式 - 减小按钮高度
        button_style = """
            QPushButton {
                background: rgba(147, 51, 234, 0.2);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(147, 51, 234, 0.3);
                border-radius: 8px;
                padding: 8px 16px;
                color: white;
                font-weight: 500;
                font-size: 13px;
                min-width: 80px;
                max-height: 32px;
            }
            QPushButton:hover {
                background: rgba(147, 51, 234, 0.3);
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background: rgba(147, 51, 234, 0.4);
                transform: translateY(0px);
            }
        """

        secondary_button_style = """
            QPushButton {
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                padding: 8px 16px;
                color: white;
                font-weight: 500;
                font-size: 13px;
                min-width: 80px;
                max-height: 32px;
            }
            QPushButton:hover {
                background: rgba(255, 255, 255, 0.2);
            }
            QPushButton:pressed {
                background: rgba(255, 255, 255, 0.15);
            }
        """

        self.load_btn = QPushButton("🔄 加载订单")
        self.load_btn.setStyleSheet(button_style)
        button_layout.addWidget(self.load_btn)

        self.today_btn = QPushButton("📅 今日订单")
        self.today_btn.setStyleSheet(secondary_button_style)
        button_layout.addWidget(self.today_btn)

        self.all_orders_btn = QPushButton("📋 所有订单")
        self.all_orders_btn.setStyleSheet(secondary_button_style)
        button_layout.addWidget(self.all_orders_btn)

        self.refresh_btn = QPushButton("🔄 刷新")
        self.refresh_btn.setStyleSheet(secondary_button_style)
        button_layout.addWidget(self.refresh_btn)
        
        button_layout.addStretch()
        filter_layout.addLayout(button_layout, 1, 2, 1, 2)
        
        parent_layout.addWidget(filter_group)
    
    def create_main_content(self, parent_layout):
        """创建主要内容区域"""
        # 使用分割器创建左右布局
        splitter = QSplitter(Qt.Orientation.Horizontal)
        splitter.setContentsMargins(0, 0, 0, 0)

        # 左侧：订单列表
        self.create_order_table(splitter)

        # 右侧：订单详情
        self.create_order_details(splitter)

        # 设置分割器比例 - 增大主内容区域
        splitter.setSizes([700, 500])

        # 使用stretch factor让主内容区域占用大部分空间
        parent_layout.addWidget(splitter, 1)  # stretch factor = 1，占用剩余空间
    
    def create_order_table(self, parent):
        """创建订单表格"""
        table_widget = QWidget()
        table_layout = QVBoxLayout(table_widget)
        table_layout.setContentsMargins(5, 0, 5, 0)
        table_layout.setSpacing(5)

        # 表格标题 - 减小字体和间距
        table_title = QLabel("订单列表")
        table_title.setFont(QFont("Inter", 16, QFont.Weight.Bold))
        table_title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: 600;
                margin: 0px;
                padding: 5px 0;
                background: transparent;
                border: none;
            }
        """)
        table_layout.addWidget(table_title)

        # 订单表格
        self.order_table = QTableWidget()
        self.order_table.setColumnCount(7)
        self.order_table.setHorizontalHeaderLabels([
            "订单号", "供应商", "下单时间", "状态", "商品种类", "采购数量", "配送数量"
        ])

        # 设置Glassmorphism表格样式
        self.order_table.setStyleSheet("""
            QTableWidget {
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 16px;
                gridline-color: rgba(255, 255, 255, 0.1);
                selection-background-color: rgba(147, 51, 234, 0.3);
                selection-color: white;
                color: rgba(255, 255, 255, 0.9);
                font-size: 14px;
            }
            QTableWidget::item {
                padding: 12px 8px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                background: transparent;
                min-height: 25px;
            }
            QTableWidget::item:hover {
                background: rgba(255, 255, 255, 0.05);
            }
            QTableWidget::item:selected {
                background: rgba(147, 51, 234, 0.3);
                color: white;
            }
            QHeaderView::section {
                background: rgba(255, 255, 255, 0.1);
                color: white;
                padding: 12px;
                border: none;
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                font-weight: 600;
                font-size: 14px;
            }
            QHeaderView::section:hover {
                background: rgba(255, 255, 255, 0.15);
            }
        """)
        
        # 设置表格属性
        self.order_table.setAlternatingRowColors(True)
        self.order_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.order_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)

        # 设置行高
        self.order_table.verticalHeader().setDefaultSectionSize(45)  # 设置默认行高为45px

        # 设置垂直表头（序号）样式
        vertical_header = self.order_table.verticalHeader()
        vertical_header.setVisible(True)  # 显示行号
        vertical_header.setDefaultSectionSize(45)  # 行高
        vertical_header.setMinimumSectionSize(45)  # 最小行高
        vertical_header.setSectionResizeMode(QHeaderView.ResizeMode.Fixed)  # 固定行高

        # 设置垂直表头样式
        self.order_table.setStyleSheet(self.order_table.styleSheet() + """
            QHeaderView::section:vertical {
                background: rgba(255, 255, 255, 0.1);
                color: white;
                padding: 8px;
                border: none;
                border-right: 1px solid rgba(255, 255, 255, 0.2);
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                font-weight: 600;
                font-size: 13px;
                text-align: center;
                min-width: 40px;
            }
            QHeaderView::section:vertical:hover {
                background: rgba(255, 255, 255, 0.15);
            }
        """)
        
        # 设置列宽
        header = self.order_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)  # 订单号
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # 供应商
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)  # 时间
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)  # 状态
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Fixed)  # 商品种类
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Fixed)  # 采购数量
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.Fixed)  # 配送数量
        
        self.order_table.setColumnWidth(0, 120)  # 订单号
        self.order_table.setColumnWidth(2, 140)  # 时间
        self.order_table.setColumnWidth(3, 80)   # 状态
        self.order_table.setColumnWidth(4, 80)   # 商品种类
        self.order_table.setColumnWidth(5, 80)   # 采购数量
        self.order_table.setColumnWidth(6, 80)   # 配送数量
        
        table_layout.addWidget(self.order_table)
        parent.addWidget(table_widget)
    
    def create_order_details(self, parent):
        """创建订单详情区域"""
        details_widget = QWidget()
        details_layout = QVBoxLayout(details_widget)
        details_layout.setContentsMargins(5, 5, 5, 5)
        details_layout.setSpacing(5)

        # 详情标题 - 与表格标题保持一致
        details_title = QLabel("订单详情")
        details_title.setFont(QFont("Inter", 16, QFont.Weight.Bold))
        details_title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: 600;
                margin: 0px;
                padding: 5px 0;
                background: transparent;
                border: none;
            }
        """)
        details_layout.addWidget(details_title)

        # 详情内容（使用滚动区域，优化显示）
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                background: transparent;
                border: none;
            }
            QScrollBar:vertical {
                background: rgba(255, 255, 255, 0.1);
                width: 8px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background: rgba(255, 255, 255, 0.3);
                border-radius: 4px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: rgba(255, 255, 255, 0.5);
            }
        """)

        self.details_content = QWidget()
        self.details_layout = QVBoxLayout(self.details_content)
        self.details_layout.setContentsMargins(5, 5, 5, 5)
        self.details_layout.setSpacing(5)

        # 默认显示提示信息
        self.show_default_details()

        scroll_area.setWidget(self.details_content)
        details_layout.addWidget(scroll_area)

        parent.addWidget(details_widget)
    
    def create_status_bar(self, parent_layout):
        """创建状态栏"""
        status_frame = QFrame()
        status_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                padding: 5px 10px;
                max-height: 30px;
            }
        """)

        status_layout = QHBoxLayout(status_frame)
        status_layout.setContentsMargins(5, 2, 5, 2)

        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.9);
                font-weight: 500;
                font-size: 11px;
                background: transparent;
                border: none;
            }
        """)
        status_layout.addWidget(self.status_label)

        status_layout.addStretch()

        self.count_label = QLabel("订单数量: 0")
        self.count_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.7);
                font-size: 11px;
                background: transparent;
                border: none;
            }
        """)
        status_layout.addWidget(self.count_label)

        parent_layout.addWidget(status_frame)
    
    def setup_connections(self):
        """设置信号连接"""
        self.load_btn.clicked.connect(self.load_orders_by_date)
        self.today_btn.clicked.connect(self.load_today_orders)
        self.all_orders_btn.clicked.connect(self.load_all_orders)
        self.refresh_btn.clicked.connect(self.refresh_current_view)
        
        # 表格选择变化
        self.order_table.itemSelectionChanged.connect(self.on_order_selected)
        
        # 搜索框变化
        self.supplier_edit.textChanged.connect(self.filter_orders)
        self.trade_no_edit.textChanged.connect(self.filter_orders)
    
    def set_api(self, api: OrderAPI):
        """设置API实例"""
        self.api = api
    
    def show_default_details(self):
        """显示默认详情信息"""
        # 安全地清空现有内容
        while self.details_layout.count():
            child = self.details_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        # 添加提示信息
        tip_label = QLabel("请选择一个订单查看详细信息")
        tip_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        tip_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.6);
                font-size: 14px;
                padding: 20px;
                background: transparent;
                border: none;
            }
        """)
        self.details_layout.addWidget(tip_label)
        self.details_layout.addStretch()

    def load_orders_by_date(self):
        """按日期加载订单"""
        if not self.api:
            QMessageBox.warning(self, "警告", "API未初始化，请先登录")
            return

        selected_date = self.date_edit.date().toString("yyyy-MM-dd")
        self.load_orders(selected_date)

    def load_today_orders(self):
        """加载今日订单"""
        if not self.api:
            QMessageBox.warning(self, "警告", "API未初始化，请先登录")
            return

        today = date.today().strftime('%Y-%m-%d')
        self.date_edit.setDate(QDate.fromString(today, "yyyy-MM-dd"))
        self.load_orders(today)

    def load_all_orders(self):
        """加载所有订单"""
        if not self.api:
            QMessageBox.warning(self, "警告", "API未初始化，请先登录")
            return

        self.load_orders(None)

    def refresh_current_view(self):
        """刷新当前视图"""
        if not self.api:
            QMessageBox.warning(self, "警告", "API未初始化，请先登录")
            return

        # 根据当前状态决定刷新方式
        if self.date_edit.date() == QDate.currentDate():
            self.load_today_orders()
        else:
            self.load_orders_by_date()

    def load_orders(self, day: Optional[str]):
        """加载订单数据"""
        if self.load_thread and self.load_thread.isRunning():
            return

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("正在加载订单...")

        # 禁用按钮
        self.set_buttons_enabled(False)

        # 创建并启动加载线程
        self.load_thread = OrderLoadThread(self.api, day)
        self.load_thread.orders_loaded.connect(self.on_orders_loaded)
        self.load_thread.error_occurred.connect(self.on_load_error)
        self.load_thread.progress_updated.connect(self.progress_bar.setValue)
        self.load_thread.finished.connect(self.on_load_finished)
        self.load_thread.start()

    def on_orders_loaded(self, result: Dict[str, Any]):
        """订单加载完成处理"""
        try:
            orders = result.get('data', [])
            total = result.get('total', 0)

            self.current_orders = orders
            self.populate_order_table(orders)

            self.status_label.setText(f"加载完成，共 {total} 个订单")
            self.count_label.setText(f"订单数量: {total}")

        except Exception as e:
            self.on_load_error(f"处理订单数据时出错: {str(e)}")

    def on_load_error(self, error_msg: str):
        """加载错误处理"""
        QMessageBox.critical(self, "错误", error_msg)
        self.status_label.setText("加载失败")

    def on_load_finished(self):
        """加载完成后的清理工作"""
        self.progress_bar.setVisible(False)
        self.set_buttons_enabled(True)

    def set_buttons_enabled(self, enabled: bool):
        """设置按钮启用状态"""
        self.load_btn.setEnabled(enabled)
        self.today_btn.setEnabled(enabled)
        self.all_orders_btn.setEnabled(enabled)
        self.refresh_btn.setEnabled(enabled)

    def populate_order_table(self, orders: List[Dict[str, Any]]):
        """填充订单表格"""
        self.order_table.setRowCount(len(orders))

        # 确保每行都有正确的高度
        for i in range(len(orders)):
            self.order_table.setRowHeight(i, 45)

        # 状态映射
        state_map = {
            0: "待处理",
            1: "处理中",
            2: "已完成",
            3: "已取消"
        }

        for row, order in enumerate(orders):
            # 订单号
            trade_no_item = QTableWidgetItem(order.get('trade_no', ''))
            trade_no_item.setData(Qt.ItemDataRole.UserRole, order)  # 存储完整订单数据
            trade_no_item.setTextAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
            self.order_table.setItem(row, 0, trade_no_item)

            # 供应商
            supplier_item = QTableWidgetItem(order.get('circulate_name', ''))
            supplier_item.setTextAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
            self.order_table.setItem(row, 1, supplier_item)

            # 下单时间
            collect_time = order.get('collect_time', '')
            if collect_time:
                try:
                    # 格式化时间显示
                    dt = datetime.strptime(collect_time, '%Y-%m-%d %H:%M:%S')
                    formatted_time = dt.strftime('%m-%d %H:%M')
                except:
                    formatted_time = collect_time
            else:
                formatted_time = ''
            time_item = QTableWidgetItem(formatted_time)
            time_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter | Qt.AlignmentFlag.AlignVCenter)
            self.order_table.setItem(row, 2, time_item)

            # 状态
            state = order.get('state', 0)
            state_text = state_map.get(state, '未知')
            state_item = QTableWidgetItem(state_text)
            state_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter | Qt.AlignmentFlag.AlignVCenter)

            # 根据状态设置颜色
            if state == 0:  # 待处理
                state_item.setBackground(Qt.GlobalColor.yellow)
            elif state == 1:  # 处理中
                state_item.setBackground(Qt.GlobalColor.cyan)
            elif state == 2:  # 已完成
                state_item.setBackground(Qt.GlobalColor.green)
            elif state == 3:  # 已取消
                state_item.setBackground(Qt.GlobalColor.red)

            self.order_table.setItem(row, 3, state_item)

            # 商品种类
            item_count = QTableWidgetItem(str(order.get('item_count', 0)))
            item_count.setTextAlignment(Qt.AlignmentFlag.AlignCenter | Qt.AlignmentFlag.AlignVCenter)
            self.order_table.setItem(row, 4, item_count)

            # 采购数量
            buy_quantity = QTableWidgetItem(str(order.get('buy_quantity', '0')))
            buy_quantity.setTextAlignment(Qt.AlignmentFlag.AlignCenter | Qt.AlignmentFlag.AlignVCenter)
            self.order_table.setItem(row, 5, buy_quantity)

            # 配送数量
            deliver_quantity = QTableWidgetItem(str(order.get('deliver_quantity', '0')))
            deliver_quantity.setTextAlignment(Qt.AlignmentFlag.AlignCenter | Qt.AlignmentFlag.AlignVCenter)
            self.order_table.setItem(row, 6, deliver_quantity)

    def on_order_selected(self):
        """订单选择变化处理"""
        try:
            current_row = self.order_table.currentRow()
            if current_row >= 0 and current_row < len(self.current_orders):
                # 直接从当前订单列表获取数据，避免从表格项获取可能为空的数据
                order_data = self.current_orders[current_row]
                if order_data:
                    self.show_order_details(order_data)
        except Exception as e:
            print(f"订单选择处理错误: {e}")
            # 不显示错误对话框，避免进一步的问题

    def show_order_details(self, order: Dict[str, Any]):
        """显示订单详情"""
        try:
            # 安全地清空现有内容
            while self.details_layout.count():
                child = self.details_layout.takeAt(0)
                if child.widget():
                    child.widget().deleteLater()

            # 显示加载提示
            loading_label = QLabel("正在加载订单详情...")
            loading_label.setStyleSheet("""
                QLabel {
                    color: white;
                    font-size: 14px;
                    padding: 20px;
                    text-align: center;
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 8px;
                }
            """)
            self.details_layout.addWidget(loading_label)

            # 获取订单号
            order_id = order.get('trade_no', '')
            if not order_id:
                self.show_detail_error("订单号为空，无法获取详情")
                return

            # 启动异步获取详情
            self.load_order_details_async(order_id, order)

        except Exception as e:
            print(f"显示订单详情错误: {e}")
            self.show_detail_error(f"显示订单详情时出错: {str(e)}")

    def show_detail_error(self, error_message: str):
        """显示详情错误信息"""
        # 清空现有内容
        while self.details_layout.count():
            child = self.details_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        error_label = QLabel(error_message)
        error_label.setStyleSheet("""
            QLabel {
                color: #ff6b6b;
                font-size: 14px;
                padding: 20px;
                text-align: center;
                background: rgba(255, 107, 107, 0.1);
                border: 1px solid rgba(255, 107, 107, 0.3);
                border-radius: 8px;
            }
        """)
        self.details_layout.addWidget(error_label)
        self.details_layout.addStretch()

    def load_order_details_async(self, order_id: str, basic_order: Dict[str, Any]):
        """异步加载订单详情"""
        # 检查API是否可用
        if not self.api:
            self.show_detail_error("API未初始化，无法获取订单详情")
            return

        # 创建工作线程
        self.detail_worker = OrderDetailWorker(self.api, order_id, basic_order)
        self.detail_worker.finished.connect(self.on_order_details_loaded)
        self.detail_worker.error.connect(self.on_order_details_error)
        self.detail_worker.start()

    def on_order_details_loaded(self, detail_data: Dict[str, Any], basic_order: Dict[str, Any]):
        """订单详情加载完成处理"""
        try:
            # 清空现有内容
            while self.details_layout.count():
                child = self.details_layout.takeAt(0)
                if child.widget():
                    child.widget().deleteLater()

            print(f"📦 订单详情加载成功")
            print(f"📋 基本订单数据: {basic_order}")
            print(f"📊 详细数据: {detail_data}")

            # 检查详细数据的关键字段
            key_fields = ['trade_no', 'item_count', 'receive_item_count',
                         'buy_quantity', 'deliver_quantity', 'count', 'goods']

            print(f"🔍 详细数据关键字段检查:")
            for field in key_fields:
                value = detail_data.get(field, 'MISSING')
                print(f"  - {field}: {value}")

            # 检查商品数据
            goods = detail_data.get('goods', [])
            print(f"🛒 商品数据: 共{len(goods)}个商品")
            if goods:
                for i, good in enumerate(goods[:2]):  # 只显示前2个商品的详情
                    print(f"  商品{i+1}: {good.get('name', 'N/A')} - 采购:{good.get('buy_quantity', 'N/A')}")

            # 智能合并基本信息和详细信息
            # 如果详细数据为空或无效，优先使用基本数据
            merged_order = basic_order.copy()

            # 只有当详细数据有效时才覆盖基本数据
            for key, value in detail_data.items():
                if key == 'goods':
                    # 商品数据：如果详细数据有商品，使用详细数据；否则保留基本数据
                    if value and len(value) > 0:
                        merged_order[key] = value
                        print(f"🔄 使用详细商品数据: {len(value)}个商品")
                    else:
                        print(f"🔄 保留基本商品数据: {len(merged_order.get('goods', []))}个商品")
                elif key in ['item_count', 'receive_item_count', 'buy_quantity', 'deliver_quantity', 'count']:
                    # 数值字段：如果详细数据不为0或空，才使用详细数据
                    if value and str(value) != '0' and value != 0:
                        merged_order[key] = value
                        print(f"🔄 使用详细数据 {key}: {value}")
                    else:
                        print(f"🔄 保留基本数据 {key}: {merged_order.get(key, 'N/A')}")
                else:
                    # 其他字段：直接使用详细数据（如果存在）
                    if value is not None and value != '':
                        merged_order[key] = value

            print(f"🔗 智能合并后的订单数据关键字段:")
            for field in key_fields:
                value = merged_order.get(field, 'MISSING')
                print(f"  - {field}: {value}")

            # 显示详细的订单信息
            self.display_detailed_order_info(merged_order)

        except Exception as e:
            print(f"❌ 处理订单详情错误: {e}")
            import traceback
            traceback.print_exc()
            self.show_detail_error(f"处理订单详情时出错: {str(e)}")

    def on_order_details_error(self, error_message: str):
        """订单详情加载错误处理"""
        print(f"获取订单详情失败: {error_message}")
        self.show_detail_error(f"获取订单详情失败: {error_message}")

    def display_detailed_order_info(self, order: Dict[str, Any]):
        """显示详细的订单信息"""
        try:
            # 订单基本信息
            self.add_detail_section("📋 基本信息", [
                ("订单号", order.get('trade_no', '')),
                ("供应商", order.get('circulate_name', '')),
                ("下单时间", order.get('collect_time', '')),
                ("订单状态", self.get_state_text(order.get('state', 0))),
                ("商品种类", str(order.get('item_count', 0))),
                ("已收货种类", str(order.get('receive_item_count', 0))),
                ("采购总量", str(order.get('buy_quantity', '0'))),
                ("配送总量", str(order.get('deliver_quantity', '0'))),
                ("商品总数", str(order.get('count', 0)))
            ])

            # 详细商品信息
            goods = order.get('goods', [])
            if goods:
                self.add_detailed_goods_section(goods)
            else:
                # 如果没有详细商品信息，显示提示
                no_goods_label = QLabel("暂无商品详情信息")
                no_goods_label.setStyleSheet("""
                    QLabel {
                        color: rgba(255, 255, 255, 0.7);
                        font-size: 14px;
                        padding: 20px;
                        text-align: center;
                        background: rgba(255, 255, 255, 0.05);
                        border-radius: 8px;
                        margin: 10px 0;
                    }
                """)
                self.details_layout.addWidget(no_goods_label)

            self.details_layout.addStretch()

        except Exception as e:
            print(f"显示详细订单信息错误: {e}")
            self.show_detail_error(f"显示详细订单信息时出错: {str(e)}")

    def add_detailed_goods_section(self, goods: List[Dict[str, Any]]):
        """添加详细商品信息区块"""
        # 商品区块标题
        title_label = QLabel("🛒 商品详情")
        title_label.setFont(QFont("Inter", 14, QFont.Weight.Bold))
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                background: rgba(59, 130, 246, 0.2);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(59, 130, 246, 0.3);
                border-radius: 12px;
                padding: 12px 16px;
                margin: 10px 0 5px 0;
                font-weight: 600;
            }
        """)
        self.details_layout.addWidget(title_label)

        # 创建滚动区域用于商品列表
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                background: transparent;
                border: none;
            }
            QScrollBar:vertical {
                background: rgba(255, 255, 255, 0.1);
                width: 8px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background: rgba(255, 255, 255, 0.3);
                border-radius: 4px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: rgba(255, 255, 255, 0.5);
            }
        """)

        # 商品容器
        goods_widget = QWidget()
        goods_layout = QVBoxLayout(goods_widget)
        goods_layout.setSpacing(8)
        goods_layout.setContentsMargins(5, 5, 5, 5)

        # 遍历商品列表
        for i, good in enumerate(goods):
            goods_card = self.create_detailed_goods_card(good, i + 1)
            goods_layout.addWidget(goods_card)

        goods_layout.addStretch()
        scroll_area.setWidget(goods_widget)

        # 设置滚动区域的最大高度
        scroll_area.setMaximumHeight(400)
        self.details_layout.addWidget(scroll_area)

    def create_detailed_goods_card(self, good: Dict[str, Any], index: int) -> QWidget:
        """创建详细商品卡片"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.08);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.15);
                border-radius: 12px;
                padding: 0px;
                margin: 2px;
            }
            QFrame:hover {
                background: rgba(255, 255, 255, 0.12);
                border: 1px solid rgba(255, 255, 255, 0.25);
            }
        """)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(16, 12, 16, 12)
        layout.setSpacing(8)

        # 商品标题行
        title_layout = QHBoxLayout()

        # 序号和商品名称
        name_label = QLabel(f"{index}. {good.get('name', '未知商品')}")
        name_label.setFont(QFont("Inter", 13, QFont.Weight.Bold))
        name_label.setStyleSheet("""
            QLabel {
                color: white;
                font-weight: 600;
                font-size: 13px;
            }
        """)
        title_layout.addWidget(name_label)

        # 商品编码
        code = good.get('code', '')
        if code:
            code_label = QLabel(f"编码: {code}")
            code_label.setStyleSheet("""
                QLabel {
                    color: rgba(255, 255, 255, 0.7);
                    font-size: 11px;
                    background: rgba(147, 51, 234, 0.2);
                    padding: 4px 8px;
                    border-radius: 6px;
                }
            """)
            title_layout.addWidget(code_label)

        title_layout.addStretch()
        layout.addLayout(title_layout)

        # 商品详细信息网格
        info_layout = QGridLayout()
        info_layout.setSpacing(8)

        # 定义信息项
        info_items = [
            ("采购数量", f"{good.get('buy_quantity', '0')} {good.get('buy_unit', '')}", 0, 0),
            ("配送数量", f"{good.get('deliver_quantity', '0')} {good.get('deliver_unit', '')}", 0, 1),
            ("收货数量", f"{good.get('receive_quantity', '未收货')}", 1, 0),
            ("库存数量", f"{good.get('stock_quantity', 0)}", 1, 1),
            ("规格", good.get('spec', '无') if good.get('spec') else '无', 2, 0),
            ("库存模式", self.get_stock_mode_text(good.get('stock_mode', 1)), 2, 1),
        ]

        # 添加收货日期（如果有）
        receive_date = good.get('receive_date')
        if receive_date:
            info_items.append(("收货日期", receive_date, 3, 0))

        # 添加批次信息（如果有）
        batch = good.get('batch')
        if batch:
            info_items.append(("批次", batch, 3, 1))

        # 创建信息标签
        for label_text, value_text, row, col in info_items:
            # 标签
            label = QLabel(f"{label_text}:")
            label.setStyleSheet("""
                QLabel {
                    color: rgba(255, 255, 255, 0.8);
                    font-size: 11px;
                    font-weight: 500;
                }
            """)
            info_layout.addWidget(label, row, col * 2)

            # 值
            value = QLabel(str(value_text))
            value.setStyleSheet("""
                QLabel {
                    color: white;
                    font-size: 11px;
                    font-weight: 600;
                }
            """)
            info_layout.addWidget(value, row, col * 2 + 1)

        layout.addLayout(info_layout)

        # 添加重量提交按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        weight_submit_btn = QPushButton("⚖️ 重量提交")
        weight_submit_btn.setFont(QFont("Inter", 11, QFont.Weight.Medium))
        weight_submit_btn.setStyleSheet("""
            QPushButton {
                background: rgba(147, 51, 234, 0.2);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(147, 51, 234, 0.3);
                border-radius: 8px;
                padding: 8px 16px;
                color: white;
                font-weight: 500;
                font-size: 11px;
            }
            QPushButton:hover {
                background: rgba(147, 51, 234, 0.3);
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background: rgba(147, 51, 234, 0.4);
                transform: translateY(0px);
            }
        """)

        # 连接点击事件，传递商品数据
        weight_submit_btn.clicked.connect(lambda: self.open_weight_submission(good))

        button_layout.addWidget(weight_submit_btn)
        layout.addLayout(button_layout)

        return card

    def get_stock_mode_text(self, stock_mode: int) -> str:
        """获取库存模式文本"""
        mode_map = {
            0: "不入库",
            1: "入库",
            2: "部分入库"
        }
        return mode_map.get(stock_mode, "未知")

    def open_weight_submission(self, product_data: Dict[str, Any]):
        """打开重量提交界面"""
        try:
            # 查找主窗口
            main_window = self.find_main_window()

            if main_window and hasattr(main_window, 'open_weight_submission_page'):
                main_window.open_weight_submission_page(product_data)
            else:
                # 如果找不到主窗口或方法，显示提示信息
                from PyQt6.QtWidgets import QMessageBox
                # 显示调试信息
                debug_info = f"调试信息:\n"
                debug_info += f"主窗口: {main_window}\n"
                debug_info += f"主窗口类型: {type(main_window)}\n"
                if main_window:
                    debug_info += f"方法存在: {hasattr(main_window, 'open_weight_submission_page')}\n"
                    debug_info += f"可用方法: {[m for m in dir(main_window) if 'weight' in m.lower()]}\n"

                QMessageBox.information(
                    self,
                    "调试信息",
                    f"商品: '{product_data.get('name', '未知商品')}'\n\n{debug_info}"
                )
        except Exception as e:
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.warning(self, "错误", f"打开重量提交界面失败: {str(e)}")

    def find_main_window(self):
        """查找主窗口"""
        from PyQt6.QtWidgets import QMainWindow

        # 从当前组件开始向上查找主窗口
        widget = self
        while widget:
            if isinstance(widget, QMainWindow):
                return widget
            widget = widget.parent()

        # 如果没找到，尝试从QApplication获取
        from PyQt6.QtWidgets import QApplication
        app = QApplication.instance()
        if app:
            for widget in app.topLevelWidgets():
                if isinstance(widget, QMainWindow):
                    return widget

        return None

    def add_detail_section(self, title: str, items: List[tuple]):
        """添加详情区块"""
        # 区块标题
        title_label = QLabel(title)
        title_label.setFont(QFont("Inter", 14, QFont.Weight.Bold))
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                background: rgba(147, 51, 234, 0.2);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(147, 51, 234, 0.3);
                border-radius: 8px;
                padding: 8px 12px;
                margin: 2px 0;
                font-size: 14px;
                font-weight: 600;
            }
        """)
        self.details_layout.addWidget(title_label)

        # 详情内容容器
        content_frame = QFrame()
        content_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                padding: 10px;
                margin: 0px 0 5px 0;
            }
        """)
        content_layout = QVBoxLayout(content_frame)
        content_layout.setSpacing(5)
        content_layout.setContentsMargins(5, 5, 5, 5)

        # 详情内容
        for label, value in items:
            item_layout = QHBoxLayout()

            label_widget = QLabel(f"{label}:")
            label_widget.setStyleSheet("""
                QLabel {
                    font-weight: 600;
                    color: rgba(255, 255, 255, 0.9);
                    font-size: 14px;
                    background: transparent;
                    border: none;
                }
            """)
            label_widget.setFixedWidth(100)

            value_widget = QLabel(str(value))
            value_widget.setStyleSheet("""
                QLabel {
                    color: rgba(255, 255, 255, 0.8);
                    font-size: 14px;
                    background: transparent;
                    border: none;
                }
            """)
            value_widget.setWordWrap(True)

            item_layout.addWidget(label_widget)
            item_layout.addWidget(value_widget)
            item_layout.addStretch()

            content_layout.addLayout(item_layout)

        self.details_layout.addWidget(content_frame)

    def add_goods_section(self, goods: List[Dict[str, Any]]):
        """添加商品详情区块"""
        title_label = QLabel("商品详情")
        title_label.setFont(QFont("Microsoft YaHei", 11, QFont.Weight.Bold))
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background-color: #ecf0f1;
                padding: 8px;
                border-radius: 3px;
                margin: 5px 0;
            }
        """)
        self.details_layout.addWidget(title_label)

        for i, good in enumerate(goods, 1):
            good_frame = QFrame()
            good_frame.setFrameStyle(QFrame.Shape.StyledPanel)
            good_frame.setStyleSheet("""
                QFrame {
                    background-color: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 5px;
                    margin: 2px;
                    padding: 5px;
                }
            """)

            good_layout = QVBoxLayout(good_frame)

            # 商品标题
            good_title = QLabel(f"商品 {i}: {good.get('name', '')}")
            good_title.setFont(QFont("Microsoft YaHei", 10, QFont.Weight.Bold))
            good_title.setStyleSheet("color: #495057;")
            good_layout.addWidget(good_title)

            # 商品信息
            info_layout = QGridLayout()

            info_items = [
                ("编码", good.get('code', '')),
                ("规格", good.get('spec', '') or '无'),
                ("采购数量", f"{good.get('buy_quantity', '0')} {good.get('buy_unit', '')}"),
                ("配送数量", f"{good.get('deliver_quantity', '0')} {good.get('deliver_unit', '')}"),
                ("库存数量", str(good.get('stock_quantity', 0))),
                ("收货数量", good.get('receive_quantity', '') or '未收货')
            ]

            for idx, (label, value) in enumerate(info_items):
                row = idx // 2
                col = (idx % 2) * 2

                label_widget = QLabel(f"{label}:")
                label_widget.setStyleSheet("font-weight: bold; color: #6c757d; font-size: 10px;")

                value_widget = QLabel(str(value))
                value_widget.setStyleSheet("color: #495057; font-size: 10px;")

                info_layout.addWidget(label_widget, row, col)
                info_layout.addWidget(value_widget, row, col + 1)

            good_layout.addLayout(info_layout)
            self.details_layout.addWidget(good_frame)

    def get_state_text(self, state: int) -> str:
        """获取状态文本"""
        state_map = {
            0: "待处理",
            1: "处理中",
            2: "已完成",
            3: "已取消"
        }
        return state_map.get(state, '未知')

    def filter_orders(self):
        """筛选订单"""
        supplier_filter = self.supplier_edit.text().lower()
        trade_no_filter = self.trade_no_edit.text().lower()

        for row in range(self.order_table.rowCount()):
            show_row = True

            # 供应商筛选
            if supplier_filter:
                supplier_item = self.order_table.item(row, 1)
                if supplier_item:
                    supplier_text = supplier_item.text().lower()
                    if supplier_filter not in supplier_text:
                        show_row = False

            # 订单号筛选
            if trade_no_filter and show_row:
                trade_no_item = self.order_table.item(row, 0)
                if trade_no_item:
                    trade_no_text = trade_no_item.text().lower()
                    if trade_no_filter not in trade_no_text:
                        show_row = False

            self.order_table.setRowHidden(row, not show_row)

        # 更新显示的订单数量
        visible_count = sum(1 for row in range(self.order_table.rowCount())
                          if not self.order_table.isRowHidden(row))
        self.count_label.setText(f"显示订单: {visible_count}/{self.order_table.rowCount()}")

    def auto_load_orders(self):
        """自动加载所有订单"""
        if self.api:
            self.load_all_orders()
        else:
            # 如果API还没有设置，再延迟一段时间重试
            QTimer.singleShot(1000, self.auto_load_orders)


class OrderDetailWorker(QThread):
    """订单详情获取工作线程"""
    finished = pyqtSignal(dict, dict)  # 详情数据, 基本订单数据
    error = pyqtSignal(str)  # 错误信息

    def __init__(self, order_api, order_id: str, basic_order: Dict[str, Any]):
        super().__init__()
        self.order_api = order_api
        self.order_id = order_id
        self.basic_order = basic_order

    def run(self):
        """执行获取订单详情"""
        try:
            print(f"🔄 开始获取订单详情: {self.order_id}")

            # 检查API实例
            if not self.order_api:
                self.error.emit("API实例为空")
                return

            # 检查token
            if not hasattr(self.order_api, 'access_token') or not self.order_api.access_token:
                self.error.emit("API token未设置")
                return

            print(f"🔑 使用Token: {self.order_api.access_token[:20]}..." if len(self.order_api.access_token) > 20 else f"🔑 使用Token: {self.order_api.access_token}")

            # 先尝试flag=0（未验收），如果没有数据再尝试flag=1（已验收）
            print(f"🔄 尝试获取未验收数据 (flag=0)")
            result = self.order_api.get_order_details(self.order_id, flag=0)
            print(f"📡 API响应 (flag=0): {result}")

            # 如果未验收数据为空，尝试已验收数据
            if (result.get('code') == 200 and
                result.get('data', {}).get('count', 0) == 0 and
                len(result.get('data', {}).get('goods', [])) == 0):

                print(f"🔄 未验收数据为空，尝试获取已验收数据 (flag=1)")
                result = self.order_api.get_order_details(self.order_id, flag=1)
                print(f"📡 API响应 (flag=1): {result}")

            print(f"📊 最终使用的API响应: {result}")

            if result.get('code') == 200:
                detail_data = result.get('data', {})
                print(f"✅ 获取订单详情成功，数据长度: {len(str(detail_data))}")
                self.finished.emit(detail_data, self.basic_order)
            else:
                error_msg = result.get('msg', '获取订单详情失败')
                print(f"❌ API返回错误: {error_msg}")
                self.error.emit(f"API错误: {error_msg}")

        except Exception as e:
            print(f"❌ 获取订单详情异常: {e}")
            import traceback
            traceback.print_exc()
            self.error.emit(f"网络错误: {str(e)}")
