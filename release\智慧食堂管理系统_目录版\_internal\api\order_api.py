#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单管理API接口
Order Management API
"""

from typing import Dict, Any, List, Optional
from datetime import datetime, date
from .auth_api import AuthAPI

class OrderAPI(AuthAPI):
    """订单管理API类"""
    
    def __init__(self, base_url: str):
        """
        初始化订单管理API
        
        Args:
            base_url: API基础URL
        """
        super().__init__(base_url)
    
    def get_orders(self, day: Optional[str] = None) -> Dict[str, Any]:
        """
        获取订单列表
        
        Args:
            day: 可选的日期参数，格式为 YYYY-MM-DD，如 "2023-06-08"
                如果不提供，则获取所有订单
            
        Returns:
            订单列表响应，包含以下字段：
            - code: 响应状态码 (200表示成功)
            - msg: 响应消息
            - data: 订单数据列表，每个订单包含：
                - trade_no: 订单号
                - item_count: 商品种类数量
                - receive_item_count: 已收货商品种类数量
                - buy_quantity: 采购总数量
                - deliver_quantity: 配送总数量
                - collect_time: 下单时间
                - state: 订单状态 (0-待处理等)
                - circulate_name: 供应商名称
                - count: 商品总数
                - goods: 商品详情列表
            - total: 订单总数
        """
        # 准备请求数据
        data = {}
        if day:
            data['day'] = day
        
        # 发起POST请求
        return self.make_authenticated_request('POST', '?op=order', data=data)
    
    def get_orders_by_date_range(self, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """
        获取指定日期范围内的订单
        
        Args:
            start_date: 开始日期，格式为 YYYY-MM-DD
            end_date: 结束日期，格式为 YYYY-MM-DD
            
        Returns:
            订单列表，如果出错返回空列表
        """
        try:
            start = datetime.strptime(start_date, '%Y-%m-%d')
            end = datetime.strptime(end_date, '%Y-%m-%d')
            
            all_orders = []
            current_date = start
            
            while current_date <= end:
                date_str = current_date.strftime('%Y-%m-%d')
                result = self.get_orders(date_str)
                
                if result.get('code') == 200 and result.get('data'):
                    all_orders.extend(result['data'])
                
                current_date = current_date.replace(day=current_date.day + 1)
            
            return all_orders
            
        except Exception as e:
            print(f"获取日期范围订单失败: {e}")
            return []
    
    def get_today_orders(self) -> Dict[str, Any]:
        """
        获取今日订单

        Returns:
            今日订单响应
        """
        today = date.today().strftime('%Y-%m-%d')
        return self.get_orders(today)

    def get_order_details(self, order_id: str, flag: int = 1) -> Dict[str, Any]:
        """
        获取订单详情

        Args:
            order_id: 订单号
            flag: 记录类型，0=未验收，1=已验收

        Returns:
            订单详情响应，包含以下字段：
            - code: 响应状态码 (200表示成功)
            - msg: 响应消息
            - data: 订单详情数据，包含：
                - trade_no: 订单号
                - item_count: 商品种类数量
                - receive_item_count: 已收货商品种类数量
                - buy_quantity: 采购总数量
                - deliver_quantity: 配送总数量
                - collect_time: 下单时间
                - state: 订单状态
                - circulate_name: 供应商名称
                - count: 商品总数
                - goods: 详细商品列表，每个商品包含：
                    - id: 商品ID
                    - spec: 规格
                    - code: 商品编码
                    - name: 商品名称
                    - buy_unit: 采购单位
                    - buy_quantity: 采购数量
                    - stock_quantity: 库存数量
                    - repair_receive: 修正收货
                    - deliver_unit: 配送单位
                    - deliver_quantity: 配送数量
                    - receive_quantity: 收货数量
                    - stock_mode: 库存模式
                    - receive_date: 收货日期
                    - path: 路径
                    - batch: 批次
            - total: 总数
        """
        # 准备请求数据
        data = {
            'order_id': order_id,
            'flag': flag
        }

        # 发起POST请求
        return self.make_authenticated_request('POST', '?op=order_item', data=data)
    
    def search_orders_by_trade_no(self, trade_no: str) -> Optional[Dict[str, Any]]:
        """
        根据订单号搜索订单
        
        Args:
            trade_no: 订单号
            
        Returns:
            匹配的订单信息，如果未找到返回None
        """
        # 先获取所有订单（不指定日期）
        result = self.get_orders()
        
        if result.get('code') == 200 and result.get('data'):
            for order in result['data']:
                if order.get('trade_no') == trade_no:
                    return order
        
        return None
    
    def search_orders_by_supplier(self, supplier_name: str) -> List[Dict[str, Any]]:
        """
        根据供应商名称搜索订单
        
        Args:
            supplier_name: 供应商名称（支持部分匹配）
            
        Returns:
            匹配的订单列表
        """
        result = self.get_orders()
        matched_orders = []
        
        if result.get('code') == 200 and result.get('data'):
            for order in result['data']:
                circulate_name = order.get('circulate_name', '')
                if supplier_name.lower() in circulate_name.lower():
                    matched_orders.append(order)
        
        return matched_orders
    
    def get_order_statistics(self, day: Optional[str] = None) -> Dict[str, Any]:
        """
        获取订单统计信息
        
        Args:
            day: 可选的日期参数
            
        Returns:
            统计信息字典，包含：
            - total_orders: 订单总数
            - total_items: 商品种类总数
            - total_buy_quantity: 采购总数量
            - total_deliver_quantity: 配送总数量
            - suppliers: 供应商列表
            - order_states: 订单状态统计
        """
        result = self.get_orders(day)
        
        if result.get('code') != 200 or not result.get('data'):
            return {
                'total_orders': 0,
                'total_items': 0,
                'total_buy_quantity': 0,
                'total_deliver_quantity': 0,
                'suppliers': [],
                'order_states': {}
            }
        
        orders = result['data']
        suppliers = set()
        order_states = {}
        total_buy_quantity = 0
        total_deliver_quantity = 0
        total_items = 0
        
        for order in orders:
            # 统计供应商
            supplier = order.get('circulate_name', '')
            if supplier:
                suppliers.add(supplier)
            
            # 统计订单状态
            state = order.get('state', 0)
            order_states[state] = order_states.get(state, 0) + 1
            
            # 统计数量
            try:
                buy_qty = float(order.get('buy_quantity', 0))
                deliver_qty = float(order.get('deliver_quantity', 0))
                total_buy_quantity += buy_qty
                total_deliver_quantity += deliver_qty
            except (ValueError, TypeError):
                pass
            
            # 统计商品种类
            total_items += order.get('item_count', 0)
        
        return {
            'total_orders': len(orders),
            'total_items': total_items,
            'total_buy_quantity': round(total_buy_quantity, 2),
            'total_deliver_quantity': round(total_deliver_quantity, 2),
            'suppliers': list(suppliers),
            'order_states': order_states
        }
    
    def format_order_for_display(self, order: Dict[str, Any]) -> Dict[str, Any]:
        """
        格式化订单数据用于显示
        
        Args:
            order: 原始订单数据
            
        Returns:
            格式化后的订单数据
        """
        # 状态映射
        state_map = {
            0: "待处理",
            1: "处理中", 
            2: "已完成",
            3: "已取消"
        }
        
        # 格式化商品列表
        goods_summary = []
        if order.get('goods'):
            for good in order['goods']:
                goods_summary.append({
                    'name': good.get('name', ''),
                    'code': good.get('code', ''),
                    'buy_quantity': good.get('buy_quantity', '0'),
                    'buy_unit': good.get('buy_unit', ''),
                    'deliver_quantity': good.get('deliver_quantity', '0'),
                    'deliver_unit': good.get('deliver_unit', ''),
                    'stock_quantity': good.get('stock_quantity', 0)
                })
        
        return {
            'trade_no': order.get('trade_no', ''),
            'supplier': order.get('circulate_name', ''),
            'order_time': order.get('collect_time', ''),
            'state': state_map.get(order.get('state', 0), '未知'),
            'state_code': order.get('state', 0),
            'item_count': order.get('item_count', 0),
            'buy_quantity': order.get('buy_quantity', '0'),
            'deliver_quantity': order.get('deliver_quantity', '0'),
            'goods': goods_summary,
            'goods_count': len(goods_summary)
        }
