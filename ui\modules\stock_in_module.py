#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
入库管理模块
Stock In Management Module
"""

import os
import json
from datetime import datetime
from typing import Dict, Any, List, Optional

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QTableWidget, QTableWidgetItem,
    QComboBox, QLineEdit, QDateEdit, QSpinBox, QDoubleSpinBox,
    QGroupBox, QFrame, QTabWidget, QMessageBox, QProgressBar,
    QHeaderView, QAbstractItemView, QSplitter, QTextEdit
)
from PyQt6.QtCore import (
    Qt, QThread, pyqtSignal, QDate, QTimer
)
from PyQt6.QtGui import QFont, QPixmap

# 导入API
from api.stock_in_api import StockInAPI

# 导入摄像头和串口模块
try:
    from .camera_module import CameraModule
except ImportError:
    CameraModule = None
    print("警告: 摄像头模块导入失败")

try:
    from .weight_submission_module import SerialWorker
except ImportError:
    SerialWorker = None
    print("警告: 串口模块导入失败")

from ..styles import GlassmorphismStyles


class StockInDataLoader(QThread):
    """入库数据加载线程"""
    data_loaded = pyqtSignal(str, dict)  # 数据类型, 数据
    error_occurred = pyqtSignal(str)
    progress_updated = pyqtSignal(int)

    def __init__(self, api: StockInAPI, data_type: str):
        super().__init__()
        self.api = api
        self.data_type = data_type

    def run(self):
        """运行数据加载"""
        try:
            self.progress_updated.emit(10)
            
            if self.data_type == "products":
                result = self.api.get_products_list()
            elif self.data_type == "suppliers":
                result = self.api.get_suppliers_list()
            elif self.data_type == "depots":
                result = self.api.get_depot_list()
            elif self.data_type == "stock_in_index":
                result = self.api.get_stock_in_index()
            else:
                raise ValueError(f"未知的数据类型: {self.data_type}")
            
            self.progress_updated.emit(80)
            
            if result.get('code') == 200:
                self.data_loaded.emit(self.data_type, result)
            else:
                self.error_occurred.emit(f"加载{self.data_type}失败: {result.get('msg', '未知错误')}")
            
            self.progress_updated.emit(100)
            
        except Exception as e:
            self.error_occurred.emit(f"加载{self.data_type}时发生错误: {str(e)}")


class StockInSubmitThread(QThread):
    """入库提交线程"""
    submit_completed = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    progress_updated = pyqtSignal(int)

    def __init__(self, api: StockInAPI, submit_data: Dict[str, Any]):
        super().__init__()
        self.api = api
        self.submit_data = submit_data

    def run(self):
        """运行入库提交"""
        try:
            self.progress_updated.emit(20)
            
            # 验证数据
            validation_result = self.api.validate_stock_in_data(
                code=self.submit_data['code'],
                datetime=self.submit_data['datetime'],
                depot_code=self.submit_data['depot_code'],
                company_code=self.submit_data['company_code'],
                details=self.submit_data['details']
            )
            
            self.progress_updated.emit(40)
            
            if not validation_result['valid']:
                self.error_occurred.emit(f"数据验证失败: {'; '.join(validation_result['errors'])}")
                return
            
            self.progress_updated.emit(60)
            
            # 提交入库申请
            result = self.api.submit_stock_in(
                code=self.submit_data['code'],
                datetime=self.submit_data['datetime'],
                depot_code=self.submit_data['depot_code'],
                company_code=self.submit_data['company_code'],
                details=self.submit_data['details']
            )
            
            self.progress_updated.emit(100)
            self.submit_completed.emit(result)
            
        except Exception as e:
            self.error_occurred.emit(f"提交入库申请时发生错误: {str(e)}")


class StockInModule(QWidget):
    """入库管理模块"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.api = None
        
        # 数据存储
        self.depot_data = []
        self.supplier_data = []
        self.product_data = []
        self.selected_products = []  # 选中的商品
        self.current_stock_in_code = ""  # 当前入库单号
        
        # 线程管理
        self.loader_threads = {}
        self.submit_thread = None
        
        # 串口和摄像头
        self.serial_worker = None
        self.camera_module = None
        self.current_weight = "0.0"
        self.captured_photos = []
        
        self.init_ui()
        self.apply_styles()
        self.setup_connections()

        # 设置串口连接
        self.setup_serial_connection()

    def init_ui(self):
        """初始化用户界面"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：基础信息和商品选择
        left_panel = self.create_left_panel()
        splitter.addWidget(left_panel)
        
        # 右侧：操作区域
        right_panel = self.create_right_panel()
        splitter.addWidget(right_panel)
        
        # 设置分割比例
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 1)
        
        layout.addWidget(splitter)

    def create_left_panel(self):
        """创建左侧面板"""
        widget = QFrame()
        widget.setObjectName("leftPanel")
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)

        # 基础信息区域
        self.create_basic_info_area(layout)
        
        # 商品选择区域
        self.create_product_selection_area(layout)

        return widget

    def create_right_panel(self):
        """创建右侧面板"""
        widget = QFrame()
        widget.setObjectName("rightPanel")
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)

        # 操作区域
        self.create_operation_area(layout)

        return widget

    def set_api(self, api: StockInAPI):
        """设置API实例"""
        self.api = api
        if api:
            self.load_initial_data()

    def load_initial_data(self):
        """加载初始数据"""
        if not self.api:
            return
        
        # 加载基础数据
        data_types = ["depots", "suppliers", "products", "stock_in_index"]
        for data_type in data_types:
            self.load_data(data_type)

    def load_data(self, data_type: str):
        """加载数据"""
        if data_type in self.loader_threads:
            return  # 避免重复加载
        
        thread = StockInDataLoader(self.api, data_type)
        thread.data_loaded.connect(self.on_data_loaded)
        thread.error_occurred.connect(self.on_data_error)
        thread.finished.connect(lambda: self.loader_threads.pop(data_type, None))
        
        self.loader_threads[data_type] = thread
        thread.start()

    def on_data_loaded(self, data_type: str, result: Dict[str, Any]):
        """数据加载完成"""
        data = result.get('data', [])
        
        if data_type == "depots":
            self.depot_data = data
            self.update_depot_combo()
        elif data_type == "suppliers":
            self.supplier_data = data
            self.update_supplier_combo()
        elif data_type == "products":
            self.product_data = data
            self.update_product_table()
        elif data_type == "stock_in_index":
            self.current_stock_in_code = result.get('msg', '')
            self.update_stock_in_code_display()

    def on_data_error(self, error_msg: str):
        """数据加载错误"""
        QMessageBox.warning(self, "数据加载错误", error_msg)

    def create_basic_info_area(self, parent_layout):
        """创建基础信息区域"""
        group = QGroupBox("📋 入库基础信息")
        group.setObjectName("basicInfoGroup")
        layout = QGridLayout(group)
        layout.setSpacing(10)

        # 入库单号
        layout.addWidget(QLabel("入库单号:"), 0, 0)
        self.stock_in_code_label = QLabel("正在获取...")
        self.stock_in_code_label.setObjectName("stockInCodeLabel")
        layout.addWidget(self.stock_in_code_label, 0, 1)

        # 刷新单号按钮
        refresh_code_btn = QPushButton("🔄 刷新单号")
        refresh_code_btn.setObjectName("refreshCodeBtn")
        refresh_code_btn.clicked.connect(self.refresh_stock_in_code)
        layout.addWidget(refresh_code_btn, 0, 2)

        # 入库日期
        layout.addWidget(QLabel("入库日期:"), 1, 0)
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setObjectName("dateEdit")
        layout.addWidget(self.date_edit, 1, 1, 1, 2)

        # 仓库选择
        layout.addWidget(QLabel("目标仓库:"), 2, 0)
        self.depot_combo = QComboBox()
        self.depot_combo.setObjectName("depotCombo")
        layout.addWidget(self.depot_combo, 2, 1, 1, 2)

        # 供应商选择
        layout.addWidget(QLabel("供应商:"), 3, 0)
        self.supplier_combo = QComboBox()
        self.supplier_combo.setObjectName("supplierCombo")
        layout.addWidget(self.supplier_combo, 3, 1, 1, 2)

        parent_layout.addWidget(group)

    def create_product_selection_area(self, parent_layout):
        """创建商品选择区域"""
        group = QGroupBox("🛍️ 商品选择")
        group.setObjectName("productSelectionGroup")
        layout = QVBoxLayout(group)
        layout.setSpacing(10)

        # 搜索框
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("搜索商品:"))
        self.product_search_edit = QLineEdit()
        self.product_search_edit.setPlaceholderText("输入商品名称或代码...")
        self.product_search_edit.setObjectName("productSearchEdit")
        self.product_search_edit.textChanged.connect(self.filter_products)
        search_layout.addWidget(self.product_search_edit)
        layout.addLayout(search_layout)

        # 商品表格
        self.product_table = QTableWidget()
        self.product_table.setColumnCount(4)
        self.product_table.setHorizontalHeaderLabels(["选择", "商品代码", "商品名称", "单位"])
        self.product_table.setObjectName("productTable")
        self.product_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.product_table.setAlternatingRowColors(True)

        # 设置列宽
        header = self.product_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        self.product_table.setColumnWidth(0, 60)

        layout.addWidget(self.product_table)

        # 添加选中商品按钮
        add_btn = QPushButton("➕ 添加选中商品")
        add_btn.setObjectName("addProductBtn")
        add_btn.clicked.connect(self.add_selected_products)
        layout.addWidget(add_btn)

        parent_layout.addWidget(group)

    def create_operation_area(self, parent_layout):
        """创建操作区域"""
        # 选中商品信息
        self.create_selected_products_area(parent_layout)

        # 重量和照片区域
        self.create_weight_photo_area(parent_layout)

        # 提交区域
        self.create_submit_area(parent_layout)

    def create_selected_products_area(self, parent_layout):
        """创建选中商品区域"""
        group = QGroupBox("📦 入库商品明细")
        group.setObjectName("selectedProductsGroup")
        layout = QVBoxLayout(group)

        # 选中商品表格
        self.selected_table = QTableWidget()
        self.selected_table.setColumnCount(6)
        self.selected_table.setHorizontalHeaderLabels(["商品代码", "商品名称", "数量", "单位", "价格", "操作"])
        self.selected_table.setObjectName("selectedTable")
        self.selected_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.selected_table.setAlternatingRowColors(True)

        # 设置列宽
        header = self.selected_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Fixed)
        self.selected_table.setColumnWidth(2, 100)
        self.selected_table.setColumnWidth(4, 100)
        self.selected_table.setColumnWidth(5, 80)

        layout.addWidget(self.selected_table)

        parent_layout.addWidget(group)

    def create_weight_photo_area(self, parent_layout):
        """创建重量和照片区域"""
        # 使用标签页
        tab_widget = QTabWidget()
        tab_widget.setObjectName("operationTabs")

        # 重量标签页
        weight_tab = self.create_weight_tab()
        tab_widget.addTab(weight_tab, "⚖️ 重量")

        # 照片标签页
        photo_tab = self.create_photo_tab()
        tab_widget.addTab(photo_tab, "📷 照片")

        parent_layout.addWidget(tab_widget)

    def create_weight_tab(self):
        """创建重量标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)

        # 串口连接状态
        status_frame = QFrame()
        status_frame.setObjectName("statusFrame")
        status_layout = QHBoxLayout(status_frame)

        self.serial_status_label = QLabel("串口状态: 未连接")
        self.serial_status_label.setObjectName("serialStatus")

        self.connect_btn = QPushButton("🔌 连接串口")
        self.connect_btn.setObjectName("connectBtn")
        self.connect_btn.clicked.connect(self.toggle_serial_connection)

        status_layout.addWidget(self.serial_status_label)
        status_layout.addStretch()
        status_layout.addWidget(self.connect_btn)

        layout.addWidget(status_frame)

        # 重量显示
        weight_frame = QFrame()
        weight_frame.setObjectName("weightFrame")
        weight_layout = QVBoxLayout(weight_frame)

        weight_label = QLabel("当前重量")
        weight_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        weight_label.setObjectName("weightLabel")

        self.weight_display = QLabel("0.0 kg")
        self.weight_display.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.weight_display.setObjectName("weightDisplay")

        weight_layout.addWidget(weight_label)
        weight_layout.addWidget(self.weight_display)

        layout.addWidget(weight_frame)

        # 手动输入重量
        manual_frame = QFrame()
        manual_frame.setObjectName("manualFrame")
        manual_layout = QHBoxLayout(manual_frame)

        manual_layout.addWidget(QLabel("手动输入:"))
        self.manual_weight_edit = QLineEdit()
        self.manual_weight_edit.setPlaceholderText("输入重量...")
        self.manual_weight_edit.setObjectName("manualWeightEdit")

        set_weight_btn = QPushButton("设置重量")
        set_weight_btn.setObjectName("setWeightBtn")
        set_weight_btn.clicked.connect(self.set_manual_weight)

        manual_layout.addWidget(self.manual_weight_edit)
        manual_layout.addWidget(set_weight_btn)

        layout.addWidget(manual_frame)

        # 状态信息
        self.status_label = QLabel("等待重量数据...")
        self.status_label.setObjectName("statusLabel")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.status_label)

        layout.addStretch()
        return widget

    def create_photo_tab(self):
        """创建照片标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)

        # 摄像头预览
        if CameraModule:
            self.camera_module = CameraModule()
            self.camera_module.photo_captured.connect(self.on_photo_captured)
            layout.addWidget(self.camera_module)
        else:
            no_camera_label = QLabel("摄像头模块不可用")
            no_camera_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(no_camera_label)

        # 已拍摄照片列表
        photos_group = QGroupBox("📸 已拍摄照片")
        photos_layout = QVBoxLayout(photos_group)

        self.photos_list = QTextEdit()
        self.photos_list.setMaximumHeight(100)
        self.photos_list.setObjectName("photosList")
        self.photos_list.setReadOnly(True)
        photos_layout.addWidget(self.photos_list)

        clear_photos_btn = QPushButton("🗑️ 清空照片")
        clear_photos_btn.setObjectName("clearPhotosBtn")
        clear_photos_btn.clicked.connect(self.clear_photos)
        photos_layout.addWidget(clear_photos_btn)

        layout.addWidget(photos_group)
        return widget

    def create_submit_area(self, parent_layout):
        """创建提交区域"""
        group = QGroupBox("🚀 提交入库")
        group.setObjectName("submitGroup")
        layout = QVBoxLayout(group)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setObjectName("progressBar")
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        # 按钮区域
        button_layout = QHBoxLayout()

        self.submit_btn = QPushButton("📤 提交入库申请")
        self.submit_btn.setObjectName("submitBtn")
        self.submit_btn.clicked.connect(self.submit_stock_in)

        self.clear_btn = QPushButton("🗑️ 清空数据")
        self.clear_btn.setObjectName("clearBtn")
        self.clear_btn.clicked.connect(self.clear_all_data)

        button_layout.addWidget(self.submit_btn)
        button_layout.addWidget(self.clear_btn)

        layout.addLayout(button_layout)

        parent_layout.addWidget(group)

    def setup_connections(self):
        """设置信号连接"""
        pass

    def apply_styles(self):
        """应用样式"""
        styles = GlassmorphismStyles()
        self.setStyleSheet(styles.get_stock_in_styles())

    def setup_serial_connection(self):
        """设置串口连接"""
        if not SerialWorker:
            self.serial_status_label.setText("串口模块不可用")
            self.connect_btn.setEnabled(False)
            return

        try:
            # 从配置文件获取串口设置
            from config.settings import settings

            port = settings.serial_port
            baudrate = settings.serial_baudrate
            timeout = settings.serial_timeout
            auto_detect = settings.serial_auto_detect

            self.serial_worker = SerialWorker(port, baudrate, timeout, auto_detect)
            self.serial_worker.weight_received.connect(self.on_weight_received)
            self.serial_worker.error_occurred.connect(self.on_serial_error)
            self.serial_worker.connection_status.connect(self.on_serial_status_changed)
            self.serial_worker.port_detected.connect(self.on_port_detected)
            self.serial_worker.start()

            if auto_detect:
                self.serial_status_label.setText("正在检测串口...")
            else:
                self.serial_status_label.setText(f"正在连接串口 {port}...")

        except Exception as e:
            self.on_serial_error(f"启动串口监听失败: {str(e)}")

    def toggle_serial_connection(self):
        """切换串口连接状态"""
        if not self.serial_worker:
            self.setup_serial_connection()
            return

        # 使用 running 属性而不是 is_connected
        if self.serial_worker.running:
            self.cleanup_serial_connection()
        else:
            self.setup_serial_connection()

    def cleanup_serial_connection(self):
        """清理串口连接"""
        if self.serial_worker:
            print("🔌 入库管理模块开始断开串口连接")
            self.serial_worker.stop()

            # 使用超时机制避免无限等待
            if not self.serial_worker.wait(3000):  # 等待最多3秒
                print("⚠️ 串口线程停止超时，强制终止")
                self.serial_worker.terminate()
                self.serial_worker.wait(1000)  # 再等待1秒

            self.serial_worker = None
            print("✅ 入库管理模块串口连接已清理")

        self.serial_status_label.setText("串口状态: 未连接")
        self.connect_btn.setText("🔌 连接串口")

    def on_weight_received(self, weight: str):
        """接收到重量数据"""
        try:
            # 解析重量值
            weight_value = float(weight.replace('kg', '').strip())
            self.current_weight = f"{weight_value:.2f}"
            self.weight_display.setText(f"{self.current_weight} kg")
            self.status_label.setText(f"重量更新: {self.current_weight} kg")
        except ValueError:
            pass

    def on_serial_error(self, error_msg: str):
        """串口错误处理"""
        self.serial_status_label.setText(f"串口错误: {error_msg}")
        self.status_label.setText(f"串口错误: {error_msg}")

    def on_serial_status_changed(self, is_connected: bool):
        """串口连接状态改变"""
        if is_connected:
            self.serial_status_label.setText("串口状态: 已连接")
            self.connect_btn.setText("🔌 断开串口")
            self.status_label.setText("串口连接成功，等待重量数据...")
        else:
            self.serial_status_label.setText("串口状态: 未连接")
            self.connect_btn.setText("🔌 连接串口")
            self.status_label.setText("串口连接断开")

    def on_port_detected(self, port: str):
        """检测到串口"""
        self.status_label.setText(f"检测到串口设备: {port}")

    def set_manual_weight(self):
        """设置手动输入的重量"""
        try:
            weight_text = self.manual_weight_edit.text().strip()
            weight_value = float(weight_text)
            self.current_weight = f"{weight_value:.2f}"
            self.weight_display.setText(f"{self.current_weight} kg")
            self.status_label.setText(f"手动设置重量: {self.current_weight} kg")
            self.manual_weight_edit.clear()
        except ValueError:
            QMessageBox.warning(self, "错误", "请输入有效的重量数值")

    def on_photo_captured(self, photo_path: str):
        """照片拍摄完成"""
        self.captured_photos.append(photo_path)
        self.update_photos_list()
        self.status_label.setText(f"已拍摄 {len(self.captured_photos)} 张照片")

    def update_photos_list(self):
        """更新照片列表显示"""
        if self.captured_photos:
            photos_text = "\n".join([f"📷 {os.path.basename(photo)}" for photo in self.captured_photos])
            self.photos_list.setText(photos_text)
        else:
            self.photos_list.setText("暂无照片")

    def clear_photos(self):
        """清空照片"""
        self.captured_photos.clear()
        self.update_photos_list()
        self.status_label.setText("已清空所有照片")

    def refresh_stock_in_code(self):
        """刷新入库单号"""
        if self.api:
            self.load_data("stock_in_index")

    def update_depot_combo(self):
        """更新仓库下拉框"""
        self.depot_combo.clear()
        for depot in self.depot_data:
            self.depot_combo.addItem(f"{depot.get('name', '')} ({depot.get('code', '')})", depot.get('code'))

    def update_supplier_combo(self):
        """更新供应商下拉框"""
        self.supplier_combo.clear()
        for supplier in self.supplier_data:
            self.supplier_combo.addItem(f"{supplier.get('name', '')} ({supplier.get('code', '')})", supplier.get('code'))

    def update_stock_in_code_display(self):
        """更新入库单号显示"""
        if self.current_stock_in_code:
            self.stock_in_code_label.setText(self.current_stock_in_code)
        else:
            self.stock_in_code_label.setText("获取失败")

    def update_product_table(self):
        """更新商品表格"""
        self.product_table.setRowCount(len(self.product_data))

        for row, product in enumerate(self.product_data):
            # 选择复选框
            checkbox_item = QTableWidgetItem()
            checkbox_item.setFlags(Qt.ItemFlag.ItemIsUserCheckable | Qt.ItemFlag.ItemIsEnabled)
            checkbox_item.setCheckState(Qt.CheckState.Unchecked)
            self.product_table.setItem(row, 0, checkbox_item)

            # 商品信息
            self.product_table.setItem(row, 1, QTableWidgetItem(product.get('code', '')))
            self.product_table.setItem(row, 2, QTableWidgetItem(product.get('name', '')))
            self.product_table.setItem(row, 3, QTableWidgetItem(product.get('unit', '')))

    def filter_products(self):
        """过滤商品"""
        search_text = self.product_search_edit.text().lower()

        for row in range(self.product_table.rowCount()):
            show_row = True
            if search_text:
                # 检查商品代码和名称
                code_item = self.product_table.item(row, 1)
                name_item = self.product_table.item(row, 2)

                code_text = code_item.text().lower() if code_item else ""
                name_text = name_item.text().lower() if name_item else ""

                show_row = search_text in code_text or search_text in name_text

            self.product_table.setRowHidden(row, not show_row)

    def add_selected_products(self):
        """添加选中的商品"""
        selected_products = []

        for row in range(self.product_table.rowCount()):
            checkbox_item = self.product_table.item(row, 0)
            if checkbox_item and checkbox_item.checkState() == Qt.CheckState.Checked:
                product = {
                    'code': self.product_table.item(row, 1).text(),
                    'name': self.product_table.item(row, 2).text(),
                    'unit': self.product_table.item(row, 3).text(),
                    'quantity': '0.0',
                    'price': '0.00'
                }
                selected_products.append(product)
                # 取消选中
                checkbox_item.setCheckState(Qt.CheckState.Unchecked)

        if not selected_products:
            QMessageBox.information(self, "提示", "请先选择要添加的商品")
            return

        # 添加到选中商品列表
        for product in selected_products:
            # 检查是否已存在
            exists = False
            for existing in self.selected_products:
                if existing['code'] == product['code']:
                    exists = True
                    break

            if not exists:
                self.selected_products.append(product)

        self.update_selected_products_table()

    def update_selected_products_table(self):
        """更新选中商品表格"""
        self.selected_table.setRowCount(len(self.selected_products))

        for row, product in enumerate(self.selected_products):
            # 商品代码
            self.selected_table.setItem(row, 0, QTableWidgetItem(product['code']))

            # 商品名称
            self.selected_table.setItem(row, 1, QTableWidgetItem(product['name']))

            # 数量输入框
            quantity_edit = QDoubleSpinBox()
            quantity_edit.setRange(0.01, 9999.99)
            quantity_edit.setDecimals(2)
            quantity_edit.setValue(float(product['quantity']))
            quantity_edit.valueChanged.connect(lambda value, r=row: self.update_product_quantity(r, value))
            self.selected_table.setCellWidget(row, 2, quantity_edit)

            # 单位
            self.selected_table.setItem(row, 3, QTableWidgetItem(product['unit']))

            # 价格输入框
            price_edit = QDoubleSpinBox()
            price_edit.setRange(0.01, 9999.99)
            price_edit.setDecimals(2)
            price_edit.setValue(float(product['price']))
            price_edit.valueChanged.connect(lambda value, r=row: self.update_product_price(r, value))
            self.selected_table.setCellWidget(row, 4, price_edit)

            # 删除按钮
            delete_btn = QPushButton("🗑️")
            delete_btn.setObjectName("deleteBtn")
            delete_btn.clicked.connect(lambda checked, r=row: self.remove_selected_product(r))
            self.selected_table.setCellWidget(row, 5, delete_btn)

    def update_product_quantity(self, row: int, value: float):
        """更新商品数量"""
        if 0 <= row < len(self.selected_products):
            self.selected_products[row]['quantity'] = f"{value:.2f}"

    def update_product_price(self, row: int, value: float):
        """更新商品价格"""
        if 0 <= row < len(self.selected_products):
            self.selected_products[row]['price'] = f"{value:.2f}"

    def remove_selected_product(self, row: int):
        """移除选中的商品"""
        if 0 <= row < len(self.selected_products):
            product_name = self.selected_products[row]['name']
            reply = QMessageBox.question(
                self, "确认删除",
                f"确定要删除商品 '{product_name}' 吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                self.selected_products.pop(row)
                self.update_selected_products_table()

    def use_current_weight_for_selected(self):
        """为选中商品使用当前重量"""
        current_row = self.selected_table.currentRow()
        if current_row >= 0 and current_row < len(self.selected_products):
            try:
                weight_value = float(self.current_weight)
                self.selected_products[current_row]['quantity'] = f"{weight_value:.2f}"
                self.update_selected_products_table()
                self.status_label.setText(f"已为商品设置重量: {weight_value:.2f} kg")
            except ValueError:
                QMessageBox.warning(self, "错误", "当前重量数据无效")
        else:
            QMessageBox.information(self, "提示", "请先选择要设置重量的商品行")

    def submit_stock_in(self):
        """提交入库申请"""
        if not self.api:
            QMessageBox.warning(self, "错误", "API未初始化")
            return

        # 验证基础信息
        if not self.current_stock_in_code:
            QMessageBox.warning(self, "错误", "请先获取入库单号")
            return

        if self.depot_combo.currentIndex() < 0:
            QMessageBox.warning(self, "错误", "请选择目标仓库")
            return

        if self.supplier_combo.currentIndex() < 0:
            QMessageBox.warning(self, "错误", "请选择供应商")
            return

        if not self.selected_products:
            QMessageBox.warning(self, "错误", "请添加入库商品")
            return

        # 验证商品数据
        for i, product in enumerate(self.selected_products):
            try:
                quantity = float(product['quantity'])
                price = float(product['price'])
                if quantity <= 0:
                    QMessageBox.warning(self, "错误", f"第{i+1}个商品的数量必须大于0")
                    return
                if price <= 0:
                    QMessageBox.warning(self, "错误", f"第{i+1}个商品的价格必须大于0")
                    return
            except ValueError:
                QMessageBox.warning(self, "错误", f"第{i+1}个商品的数量或价格格式错误")
                return

        # 确认提交
        total_items = len(self.selected_products)
        total_quantity = sum(float(p['quantity']) for p in self.selected_products)
        total_amount = sum(float(p['quantity']) * float(p['price']) for p in self.selected_products)

        confirm_msg = f"""
确认提交入库申请？

入库单号: {self.current_stock_in_code}
入库日期: {self.date_edit.date().toString('yyyy-MM-dd')}
目标仓库: {self.depot_combo.currentText()}
供应商: {self.supplier_combo.currentText()}
商品种类: {total_items}
总数量: {total_quantity:.2f}
总金额: {total_amount:.2f}元
        """.strip()

        reply = QMessageBox.question(
            self, "确认提交", confirm_msg,
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.perform_stock_in_submission()

    def perform_stock_in_submission(self):
        """执行入库提交"""
        try:
            # 显示进度
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.submit_btn.setEnabled(False)

            # 准备提交数据
            depot_code = self.depot_combo.currentData()
            company_code = self.supplier_combo.currentData()
            datetime_str = self.date_edit.date().toString('yyyy-MM-dd')

            # 构建入库明细
            details = []
            for product in self.selected_products:
                # 构建图片路径
                photo_paths = ";".join(self.captured_photos) if self.captured_photos else ""

                detail = self.api.create_stock_in_detail(
                    code=product['code'],
                    quantity=product['quantity'],
                    unit=product['unit'],
                    price=product['price'],
                    path=photo_paths
                )
                details.append(detail)

            submit_data = {
                'code': self.current_stock_in_code,
                'datetime': datetime_str,
                'depot_code': depot_code,
                'company_code': company_code,
                'details': details
            }

            # 启动提交线程
            self.submit_thread = StockInSubmitThread(self.api, submit_data)
            self.submit_thread.submit_completed.connect(self.on_submit_completed)
            self.submit_thread.error_occurred.connect(self.on_submit_error)
            self.submit_thread.progress_updated.connect(self.progress_bar.setValue)
            self.submit_thread.finished.connect(self.on_submit_finished)
            self.submit_thread.start()

        except Exception as e:
            self.on_submit_error(f"准备提交数据时发生错误: {str(e)}")

    def on_submit_completed(self, result: Dict[str, Any]):
        """提交完成"""
        if result.get('code') == 200:
            QMessageBox.information(self, "成功", f"入库申请提交成功！\n{result.get('msg', '')}")
            self.clear_all_data()
            # 重新获取入库单号
            self.refresh_stock_in_code()
        else:
            QMessageBox.warning(self, "失败", f"入库申请提交失败：\n{result.get('msg', '未知错误')}")

    def on_submit_error(self, error_msg: str):
        """提交错误"""
        QMessageBox.critical(self, "错误", f"提交入库申请时发生错误：\n{error_msg}")

    def on_submit_finished(self):
        """提交完成（无论成功失败）"""
        self.progress_bar.setVisible(False)
        self.submit_btn.setEnabled(True)

    def clear_all_data(self):
        """清空所有数据"""
        self.selected_products.clear()
        self.update_selected_products_table()
        self.captured_photos.clear()
        self.update_photos_list()
        self.current_weight = "0.0"
        self.weight_display.setText("0.0 kg")
        self.status_label.setText("数据已清空")

    def closeEvent(self, event):
        """窗口关闭事件"""
        self.cleanup_serial_connection()
        super().closeEvent(event)
