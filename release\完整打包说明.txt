智慧食堂管理系统 - 完整打包版本
====================================

版本: v1.1.0 (完整库打包版)
更新时间: 2025年8月1日

🎯 本次重大更新内容:
====================================

📦 完整库打包:
✅ 包含PyQt6完整GUI框架
✅ 包含OpenCV摄像头库 (opencv-python 4.12.0)
✅ 包含串口通信库 (pyserial 3.5)
✅ 包含数值计算库 (numpy 2.2.6)
✅ 包含网络请求库 (requests)
✅ 包含所有必要的系统依赖

🔧 硬件支持:
✅ USB摄像头实时预览和拍照
✅ 串口重量秤数据读取
✅ 自动设备检测和连接
✅ 多种串口波特率支持

🎨 功能特性:
✅ 玻璃态现代化UI设计
✅ 智能登录和认证系统
✅ 完整的订单管理功能
✅ 库存管理和出入库
✅ 重量数据自动采集
✅ 图片自动上传

📁 文件结构:
- 智慧食堂管理系统.exe (单文件版本, 98MB)
- 智慧食堂管理系统_目录版/ (目录版本)
- config/ (配置文件)
- photos/ (拍照文件保存)
- captured_images/ (图片缓存)

🚀 使用说明:
====================================
1. 双击"智慧食堂管理系统.exe"启动程序
2. 首次使用需要进行登录认证
3. 连接USB摄像头可使用拍照功能
4. 连接串口设备可自动读取重量数据
5. 所有配置自动保存

⚠️ 系统要求:
====================================
- Windows 10/11 操作系统
- 至少4GB内存
- USB摄像头（可选）
- 串口重量秤设备（可选）
- 网络连接（用于API通信）

🔧 技术规格:
====================================
- Python 3.13.5
- PyQt6 GUI框架
- OpenCV 4.12.0 摄像头支持
- PySerial 3.5 串口通信
- NumPy 2.2.6 数值计算
- 完整的依赖库打包

📋 打包详情:
====================================
本版本使用PyInstaller 6.14.2进行打包，包含以下改进：

1. 库完整性:
   - 移除了cv2和numpy的排除设置
   - 添加了所有摄像头和串口相关模块
   - 包含了PyQt6的多媒体组件

2. 文件大小:
   - 单文件版本: 约98MB
   - 包含所有必要的DLL和依赖
   - 无需额外安装任何库

3. 兼容性:
   - 支持Windows 10/11
   - 64位系统优化
   - 自动检测硬件设备

🎯 与之前版本的区别:
====================================
- 之前版本: 40MB (缺少摄像头和串口库)
- 当前版本: 98MB (包含完整功能库)
- 新增: 摄像头拍照功能完全可用
- 新增: 串口重量秤数据读取功能
- 新增: 自动设备检测和配置

📞 技术支持:
====================================
如遇问题请联系技术支持团队

🔄 下次更新计划:
====================================
- 添加更多摄像头设备支持
- 优化串口通信稳定性
- 增加设备状态监控
- 提供更多配置选项
