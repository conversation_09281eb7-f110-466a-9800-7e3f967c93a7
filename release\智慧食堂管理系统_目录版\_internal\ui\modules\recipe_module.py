#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
食谱管理模块
Recipe Management Module
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QLineEdit, QTextEdit, QComboBox,
    QTableWidget, QTableWidgetItem, QHeaderView, QFrame,
    QMessageBox, QDialog, QFormLayout, QSpinBox, QDateEdit
)
from PyQt6.QtCore import Qt, QDate, pyqtSignal
from PyQt6.QtGui import QFont

from ..styles import GlassmorphismStyles
from api.canteen_api import CanteenAPI

class RecipeDialog(QDialog):
    """食谱编辑对话框"""
    
    def __init__(self, parent=None, recipe_data=None):
        super().__init__(parent)
        self.recipe_data = recipe_data
        self.setWindowTitle("食谱编辑" if recipe_data else "新建食谱")
        self.setFixedSize(500, 600)
        self.setModal(True)
        
        self.init_ui()
        self.apply_styles()
        
        if recipe_data:
            self.load_recipe_data()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # 表单区域
        form_widget = QFrame()
        form_layout = QFormLayout(form_widget)
        form_layout.setSpacing(15)
        
        # 食谱名称
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("请输入食谱名称")
        form_layout.addRow("食谱名称:", self.name_input)
        
        # 餐别
        self.meal_combo = QComboBox()
        self.meal_combo.addItems(["早餐", "午餐", "晚餐", "加餐"])
        form_layout.addRow("餐别:", self.meal_combo)
        
        # 日期
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        form_layout.addRow("日期:", self.date_edit)
        
        # 人数
        self.people_spin = QSpinBox()
        self.people_spin.setRange(1, 10000)
        self.people_spin.setValue(100)
        form_layout.addRow("预计人数:", self.people_spin)
        
        # 食谱描述
        self.description_text = QTextEdit()
        self.description_text.setPlaceholderText("请输入食谱描述和营养搭配说明")
        self.description_text.setMaximumHeight(120)
        form_layout.addRow("食谱描述:", self.description_text)
        
        # 食材清单
        self.ingredients_text = QTextEdit()
        self.ingredients_text.setPlaceholderText("请输入食材清单，每行一个食材")
        self.ingredients_text.setMaximumHeight(150)
        form_layout.addRow("食材清单:", self.ingredients_text)
        
        layout.addWidget(form_widget)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("保存")
        self.save_btn.clicked.connect(self.save_recipe)
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
    
    def apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
        QDialog {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(30, 30, 60, 1),
                stop:0.5 rgba(60, 30, 90, 1),
                stop:1 rgba(90, 60, 120, 1));
        }
        QFrame {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            padding: 20px;
        }
        QLabel {
            color: white;
            font-weight: 500;
        }
        """)
        
        # 应用输入框样式
        for widget in [self.name_input, self.description_text, self.ingredients_text]:
            widget.setStyleSheet(GlassmorphismStyles.get_input_style())
        
        # 应用按钮样式
        self.save_btn.setStyleSheet(GlassmorphismStyles.get_button_style())
        self.cancel_btn.setStyleSheet("""
        QPushButton {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 12px 24px;
            color: white;
            font-weight: 500;
        }
        QPushButton:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        """)
        
        # 下拉框和日期选择器样式
        combo_style = """
        QComboBox, QDateEdit {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 8px;
            color: white;
        }
        QComboBox::drop-down, QDateEdit::drop-down {
            border: none;
        }
        QComboBox::down-arrow, QDateEdit::down-arrow {
            image: none;
            border: none;
        }
        """
        
        self.meal_combo.setStyleSheet(combo_style)
        self.date_edit.setStyleSheet(combo_style)
        self.people_spin.setStyleSheet(combo_style)
    
    def load_recipe_data(self):
        """加载食谱数据"""
        if not self.recipe_data:
            return
        
        self.name_input.setText(self.recipe_data.get('name', ''))
        self.description_text.setPlainText(self.recipe_data.get('description', ''))
        self.ingredients_text.setPlainText(self.recipe_data.get('ingredients', ''))
        
        # 设置餐别
        meal = self.recipe_data.get('meal', '午餐')
        index = self.meal_combo.findText(meal)
        if index >= 0:
            self.meal_combo.setCurrentIndex(index)
        
        # 设置人数
        people = self.recipe_data.get('people', 100)
        self.people_spin.setValue(people)
    
    def save_recipe(self):
        """保存食谱"""
        name = self.name_input.text().strip()
        if not name:
            QMessageBox.warning(self, "警告", "请输入食谱名称")
            return
        
        # 收集数据
        self.result_data = {
            'name': name,
            'meal': self.meal_combo.currentText(),
            'date': self.date_edit.date().toString('yyyy-MM-dd'),
            'people': self.people_spin.value(),
            'description': self.description_text.toPlainText().strip(),
            'ingredients': self.ingredients_text.toPlainText().strip()
        }
        
        self.accept()

class RecipeModule(QWidget):
    """食谱管理模块"""
    
    def __init__(self, api: CanteenAPI = None):
        super().__init__()
        self.api = api
        self.recipes = []  # 食谱列表
        
        self.init_ui()
        self.apply_styles()
        self.load_recipes()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 工具栏
        self.create_toolbar(layout)
        
        # 食谱表格
        self.create_recipe_table(layout)
    
    def create_toolbar(self, parent_layout):
        """创建工具栏"""
        toolbar = QFrame()
        toolbar.setObjectName("toolbar")
        toolbar.setFixedHeight(80)
        
        toolbar_layout = QHBoxLayout(toolbar)
        toolbar_layout.setContentsMargins(20, 15, 20, 15)
        
        # 搜索框
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("搜索食谱...")
        self.search_input.setFixedWidth(300)
        self.search_input.textChanged.connect(self.filter_recipes)
        
        # 按钮
        self.add_btn = QPushButton("新建食谱")
        self.add_btn.clicked.connect(self.add_recipe)
        
        self.edit_btn = QPushButton("编辑")
        self.edit_btn.clicked.connect(self.edit_recipe)
        self.edit_btn.setEnabled(False)
        
        self.delete_btn = QPushButton("删除")
        self.delete_btn.clicked.connect(self.delete_recipe)
        self.delete_btn.setEnabled(False)
        
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.load_recipes)
        
        toolbar_layout.addWidget(QLabel("食谱管理"))
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.search_input)
        toolbar_layout.addWidget(self.add_btn)
        toolbar_layout.addWidget(self.edit_btn)
        toolbar_layout.addWidget(self.delete_btn)
        toolbar_layout.addWidget(self.refresh_btn)
        
        parent_layout.addWidget(toolbar)
    
    def create_recipe_table(self, parent_layout):
        """创建食谱表格"""
        self.recipe_table = QTableWidget()
        self.recipe_table.setColumnCount(6)
        self.recipe_table.setHorizontalHeaderLabels([
            "食谱名称", "餐别", "日期", "预计人数", "描述", "状态"
        ])
        
        # 设置表格属性
        header = self.recipe_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Stretch)
        
        self.recipe_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.recipe_table.setAlternatingRowColors(True)
        self.recipe_table.itemSelectionChanged.connect(self.on_selection_changed)
        
        parent_layout.addWidget(self.recipe_table)

    def apply_styles(self):
        """应用样式"""
        # 工具栏样式
        toolbar_style = """
        QFrame#toolbar {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
        }
        QLabel {
            color: white;
            font-size: 18px;
            font-weight: 600;
        }
        """

        # 表格样式
        table_style = """
        QTableWidget {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            gridline-color: rgba(255, 255, 255, 0.1);
            color: white;
        }
        QTableWidget::item {
            padding: 12px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        QTableWidget::item:selected {
            background: rgba(147, 51, 234, 0.3);
        }
        QHeaderView::section {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            padding: 12px;
            border: none;
            font-weight: 600;
        }
        """

        # 应用样式
        self.setStyleSheet(toolbar_style + table_style)

        # 搜索框样式
        self.search_input.setStyleSheet(GlassmorphismStyles.get_input_style())

        # 按钮样式
        for btn in [self.add_btn, self.refresh_btn]:
            btn.setStyleSheet(GlassmorphismStyles.get_button_style())

        # 编辑和删除按钮样式
        secondary_btn_style = """
        QPushButton {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 8px 16px;
            color: white;
            font-weight: 500;
        }
        QPushButton:hover:enabled {
            background: rgba(255, 255, 255, 0.2);
        }
        QPushButton:disabled {
            background: rgba(255, 255, 255, 0.05);
            color: rgba(255, 255, 255, 0.3);
        }
        """

        self.edit_btn.setStyleSheet(secondary_btn_style)
        self.delete_btn.setStyleSheet(secondary_btn_style)

    def load_recipes(self):
        """加载食谱列表"""
        # 模拟数据（实际应该从API获取）
        self.recipes = [
            {
                'id': '1',
                'name': '营养早餐套餐',
                'meal': '早餐',
                'date': '2024-01-15',
                'people': 150,
                'description': '包含牛奶、鸡蛋、面包等营养丰富的早餐',
                'ingredients': '牛奶 200ml\n鸡蛋 2个\n全麦面包 2片\n水果 1份',
                'status': '已发布'
            },
            {
                'id': '2',
                'name': '红烧肉套餐',
                'meal': '午餐',
                'date': '2024-01-15',
                'people': 200,
                'description': '经典红烧肉配米饭和蔬菜',
                'ingredients': '五花肉 500g\n大米 300g\n青菜 200g\n调料若干',
                'status': '草稿'
            }
        ]

        self.update_table()

    def update_table(self):
        """更新表格显示"""
        self.recipe_table.setRowCount(len(self.recipes))

        for row, recipe in enumerate(self.recipes):
            self.recipe_table.setItem(row, 0, QTableWidgetItem(recipe['name']))
            self.recipe_table.setItem(row, 1, QTableWidgetItem(recipe['meal']))
            self.recipe_table.setItem(row, 2, QTableWidgetItem(recipe['date']))
            self.recipe_table.setItem(row, 3, QTableWidgetItem(str(recipe['people'])))
            self.recipe_table.setItem(row, 4, QTableWidgetItem(recipe['description']))
            self.recipe_table.setItem(row, 5, QTableWidgetItem(recipe['status']))

    def filter_recipes(self, text):
        """过滤食谱"""
        for row in range(self.recipe_table.rowCount()):
            show = False
            for col in range(self.recipe_table.columnCount()):
                item = self.recipe_table.item(row, col)
                if item and text.lower() in item.text().lower():
                    show = True
                    break
            self.recipe_table.setRowHidden(row, not show)

    def on_selection_changed(self):
        """选择改变事件"""
        has_selection = len(self.recipe_table.selectedItems()) > 0
        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)

    def add_recipe(self):
        """添加食谱"""
        dialog = RecipeDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            # 添加新食谱到列表
            new_recipe = dialog.result_data.copy()
            new_recipe['id'] = str(len(self.recipes) + 1)
            new_recipe['status'] = '草稿'

            self.recipes.append(new_recipe)
            self.update_table()

            QMessageBox.information(self, "成功", "食谱添加成功！")

    def edit_recipe(self):
        """编辑食谱"""
        current_row = self.recipe_table.currentRow()
        if current_row < 0:
            return

        recipe = self.recipes[current_row]
        dialog = RecipeDialog(self, recipe)

        if dialog.exec() == QDialog.DialogCode.Accepted:
            # 更新食谱数据
            self.recipes[current_row].update(dialog.result_data)
            self.update_table()

            QMessageBox.information(self, "成功", "食谱更新成功！")

    def delete_recipe(self):
        """删除食谱"""
        current_row = self.recipe_table.currentRow()
        if current_row < 0:
            return

        recipe = self.recipes[current_row]
        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除食谱 '{recipe['name']}' 吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            del self.recipes[current_row]
            self.update_table()
            QMessageBox.information(self, "成功", "食谱删除成功！")
