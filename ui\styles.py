#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
玻璃态UI样式定义
Glassmorphism UI Styles Definition
"""

class GlassmorphismStyles:
    """玻璃态样式类"""
    
    # 主要颜色定义
    PRIMARY_COLOR = "rgba(147, 51, 234, 0.2)"
    PRIMARY_BORDER = "rgba(147, 51, 234, 0.3)"
    PRIMARY_HOVER = "rgba(147, 51, 234, 0.3)"
    PRIMARY_ACTIVE = "rgba(147, 51, 234, 0.4)"
    
    GLASS_LIGHT = "rgba(255, 255, 255, 0.1)"
    GLASS_MEDIUM = "rgba(255, 255, 255, 0.2)"
    GLASS_BORDER = "rgba(255, 255, 255, 0.2)"
    
    ERROR_COLOR = "rgba(239, 68, 68, 0.2)"
    ERROR_BORDER = "rgba(239, 68, 68, 0.5)"
    
    SUCCESS_COLOR = "rgba(34, 197, 94, 0.2)"
    SUCCESS_BORDER = "rgba(34, 197, 94, 0.5)"
    
    @staticmethod
    def get_main_window_style():
        """获取主窗口样式"""
        return """
        QMainWindow {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(30, 30, 60, 1),
                stop:0.5 rgba(60, 30, 90, 1),
                stop:1 rgba(90, 60, 120, 1));
        }
        """
    
    @staticmethod
    def get_glass_widget_style():
        """获取玻璃态组件基础样式"""
        return """
        QWidget {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
        }
        """
    
    @staticmethod
    def get_input_style():
        """获取输入框样式"""
        return """
        QLineEdit {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            padding: 15px 16px;
            color: white;
            font-size: 16px;
            font-weight: 500;
        }
        QLineEdit:focus {
            border: 1px solid rgba(147, 51, 234, 0.5);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 0 2px rgba(147, 51, 234, 0.2);
        }
        QLineEdit::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }
        """
    
    @staticmethod
    def get_button_style():
        """获取按钮样式"""
        return """
        QPushButton {
            background: rgba(147, 51, 234, 0.2);
            border: 1px solid rgba(147, 51, 234, 0.3);
            border-radius: 12px;
            padding: 12px 24px;
            color: white;
            font-weight: 600;
            font-size: 16px;
        }
        QPushButton:hover {
            background: rgba(147, 51, 234, 0.3);
            border: 1px solid rgba(147, 51, 234, 0.4);
        }
        QPushButton:pressed {
            background: rgba(147, 51, 234, 0.4);
        }
        QPushButton:disabled {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.3);
        }
        """
    
    @staticmethod
    def get_label_style():
        """获取标签样式"""
        return """
        QLabel {
            color: white;
            font-weight: 500;
            background: transparent;
            border: none;
        }
        """
    
    @staticmethod
    def get_card_style():
        """获取卡片样式"""
        return """
        QFrame {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
        }
        """
    
    @staticmethod
    def get_title_style():
        """获取标题样式"""
        return """
        QLabel {
            color: white;
            font-size: 32px;
            font-weight: 700;
            background: transparent;
            border: none;
        }
        """
    
    @staticmethod
    def get_subtitle_style():
        """获取副标题样式"""
        return """
        QLabel {
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            font-weight: 400;
            background: transparent;
            border: none;
        }
        """

    @staticmethod
    def get_stock_in_styles():
        """获取入库管理样式"""
        return """
        /* 主容器 */
        StockInModule {
            background: transparent;
        }

        /* 面板样式 */
        QFrame#leftPanel, QFrame#rightPanel {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            padding: 10px;
        }

        /* 分组框样式 */
        QGroupBox {
            color: white;
            font-weight: bold;
            font-size: 14px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            margin-top: 10px;
            padding-top: 10px;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 8px 0 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
        }

        /* 输入框样式 */
        QLineEdit, QComboBox, QDateEdit, QSpinBox, QDoubleSpinBox {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 8px;
            color: white;
            font-size: 12px;
        }

        QLineEdit:focus, QComboBox:focus, QDateEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus {
            border: 2px solid rgba(147, 51, 234, 0.5);
            background: rgba(255, 255, 255, 0.15);
        }

        QComboBox::drop-down {
            border: none;
            width: 20px;
        }

        QComboBox::down-arrow {
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid white;
            margin-right: 5px;
        }

        /* 表格样式 */
        QTableWidget {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            gridline-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        QTableWidget::item {
            padding: 8px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        QTableWidget::item:selected {
            background: rgba(147, 51, 234, 0.3);
        }

        QHeaderView::section {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            padding: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            font-weight: bold;
        }

        /* 按钮样式 */
        QPushButton {
            background: rgba(147, 51, 234, 0.2);
            border: 1px solid rgba(147, 51, 234, 0.3);
            border-radius: 8px;
            color: white;
            font-weight: bold;
            padding: 10px 20px;
            font-size: 12px;
        }

        QPushButton:hover {
            background: rgba(147, 51, 234, 0.3);
            border: 1px solid rgba(147, 51, 234, 0.5);
        }

        QPushButton:pressed {
            background: rgba(147, 51, 234, 0.4);
        }

        QPushButton:disabled {
            background: rgba(100, 100, 100, 0.2);
            border: 1px solid rgba(100, 100, 100, 0.3);
            color: rgba(255, 255, 255, 0.3);
        }

        /* 特殊按钮样式 */
        QPushButton#submitBtn {
            background: rgba(34, 197, 94, 0.2);
            border: 1px solid rgba(34, 197, 94, 0.3);
        }

        QPushButton#submitBtn:hover {
            background: rgba(34, 197, 94, 0.3);
            border: 1px solid rgba(34, 197, 94, 0.5);
        }

        QPushButton#clearBtn {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        QPushButton#clearBtn:hover {
            background: rgba(239, 68, 68, 0.3);
            border: 1px solid rgba(239, 68, 68, 0.5);
        }

        QPushButton#deleteBtn {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid rgba(239, 68, 68, 0.3);
            padding: 5px;
            font-size: 10px;
        }

        /* 标签页样式 */
        QTabWidget::pane {
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.05);
        }

        QTabBar::tab {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 8px 16px;
            margin-right: 2px;
            color: white;
        }

        QTabBar::tab:selected {
            background: rgba(147, 51, 234, 0.3);
            border-bottom: 2px solid rgba(147, 51, 234, 0.8);
        }

        QTabBar::tab:hover {
            background: rgba(255, 255, 255, 0.15);
        }

        /* 进度条样式 */
        QProgressBar {
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            text-align: center;
            color: white;
        }

        QProgressBar::chunk {
            background: rgba(147, 51, 234, 0.6);
            border-radius: 6px;
        }

        /* 状态标签样式 */
        QLabel#stockInCodeLabel {
            background: rgba(147, 51, 234, 0.2);
            border: 1px solid rgba(147, 51, 234, 0.3);
            border-radius: 6px;
            padding: 5px 10px;
            color: white;
            font-weight: bold;
        }

        QLabel#serialStatus {
            padding: 5px 10px;
            border-radius: 6px;
            font-weight: bold;
        }

        QLabel#weightDisplay {
            background: rgba(34, 197, 94, 0.2);
            border: 2px solid rgba(34, 197, 94, 0.3);
            border-radius: 12px;
            padding: 20px;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }

        /* 文本编辑器样式 */
        QTextEdit {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: white;
            padding: 8px;
        }
        """
