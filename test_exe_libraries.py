#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试打包的exe文件是否包含所有必要的库
Test if the packaged exe contains all necessary libraries
"""

import subprocess
import sys
import os

def test_library_imports():
    """测试关键库的导入"""
    print("🔍 测试关键库导入...")
    print("=" * 50)
    
    libraries = [
        ("PyQt6.QtWidgets", "PyQt6 GUI框架"),
        ("PyQt6.QtCore", "PyQt6 核心模块"),
        ("PyQt6.QtGui", "PyQt6 GUI模块"),
        ("PyQt6.QtNetwork", "PyQt6 网络模块"),
        ("PyQt6.QtMultimedia", "PyQt6 多媒体模块"),
        ("requests", "HTTP请求库"),
        ("cv2", "OpenCV摄像头库"),
        ("serial", "串口通信库"),
        ("serial.tools.list_ports", "串口设备列表"),
        ("numpy", "数值计算库"),
        ("json", "JSON处理"),
        ("os", "操作系统接口"),
        ("sys", "系统接口"),
        ("threading", "多线程"),
        ("datetime", "日期时间"),
        ("base64", "Base64编码"),
        ("hashlib", "哈希算法"),
        ("urllib.parse", "URL解析"),
        ("pathlib", "路径处理"),
        ("re", "正则表达式"),
        ("typing", "类型提示"),
    ]
    
    success_count = 0
    failed_libraries = []
    
    for lib_name, description in libraries:
        try:
            __import__(lib_name)
            print(f"✅ {description} ({lib_name})")
            success_count += 1
        except ImportError as e:
            print(f"❌ {description} ({lib_name}) - 失败: {e}")
            failed_libraries.append(lib_name)
        except Exception as e:
            print(f"⚠️  {description} ({lib_name}) - 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {success_count}/{len(libraries)} 个库导入成功")
    
    if failed_libraries:
        print(f"❌ 失败的库: {', '.join(failed_libraries)}")
        return False
    else:
        print("✅ 所有关键库导入成功！")
        return True

def test_camera_functionality():
    """测试摄像头功能"""
    print("\n🎥 测试摄像头功能...")
    print("=" * 50)
    
    try:
        import cv2
        
        # 尝试打开摄像头
        cap = cv2.VideoCapture(0)
        if cap.isOpened():
            print("✅ 摄像头设备可访问")
            
            # 尝试读取一帧
            ret, frame = cap.read()
            if ret:
                print(f"✅ 摄像头读取成功 - 分辨率: {frame.shape[1]}x{frame.shape[0]}")
            else:
                print("⚠️  摄像头读取失败（可能没有连接摄像头）")
            
            cap.release()
            return True
        else:
            print("⚠️  无法打开摄像头设备（可能没有连接摄像头）")
            return False
            
    except Exception as e:
        print(f"❌ 摄像头功能测试失败: {e}")
        return False

def test_serial_functionality():
    """测试串口功能"""
    print("\n🔌 测试串口功能...")
    print("=" * 50)
    
    try:
        import serial
        import serial.tools.list_ports
        
        # 列出可用串口
        ports = list(serial.tools.list_ports.comports())
        print(f"📋 发现 {len(ports)} 个串口设备:")
        
        for port in ports:
            print(f"   - {port.device}: {port.description}")
        
        if ports:
            print("✅ 串口功能正常")
            return True
        else:
            print("⚠️  没有发现串口设备")
            return True  # 没有设备不算错误
            
    except Exception as e:
        print(f"❌ 串口功能测试失败: {e}")
        return False

def test_pyqt_functionality():
    """测试PyQt6功能"""
    print("\n🖥️  测试PyQt6功能...")
    print("=" * 50)
    
    try:
        from PyQt6.QtWidgets import QApplication, QLabel, QWidget
        from PyQt6.QtCore import Qt
        from PyQt6.QtGui import QFont
        
        # 创建应用程序实例（不显示窗口）
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 创建一个简单的窗口部件
        widget = QWidget()
        label = QLabel("测试标签", widget)
        font = QFont("Arial", 12)
        label.setFont(font)
        
        print("✅ PyQt6 GUI组件创建成功")
        print("✅ 字体设置功能正常")
        print("✅ 窗口部件功能正常")
        
        return True
        
    except Exception as e:
        print(f"❌ PyQt6功能测试失败: {e}")
        return False

def test_network_functionality():
    """测试网络功能"""
    print("\n🌐 测试网络功能...")
    print("=" * 50)
    
    try:
        import requests
        import urllib.parse
        
        # 测试URL解析
        test_url = "https://httpbin.org/get"
        parsed = urllib.parse.urlparse(test_url)
        print(f"✅ URL解析功能正常: {parsed.netloc}")
        
        # 测试网络请求（简单的GET请求）
        try:
            response = requests.get("https://httpbin.org/get", timeout=5)
            if response.status_code == 200:
                print("✅ HTTP请求功能正常")
            else:
                print(f"⚠️  HTTP请求返回状态码: {response.status_code}")
        except requests.exceptions.RequestException:
            print("⚠️  网络连接测试失败（可能没有网络连接）")
        
        return True
        
    except Exception as e:
        print(f"❌ 网络功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 智慧食堂管理系统 - 库完整性测试")
    print("=" * 60)
    print("测试打包的exe文件是否包含所有必要的库和功能")
    print("=" * 60)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("库导入测试", test_library_imports()))
    test_results.append(("摄像头功能测试", test_camera_functionality()))
    test_results.append(("串口功能测试", test_serial_functionality()))
    test_results.append(("PyQt6功能测试", test_pyqt_functionality()))
    test_results.append(("网络功能测试", test_network_functionality()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print("=" * 60)
    
    passed_tests = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed_tests += 1
    
    print(f"\n总体结果: {passed_tests}/{len(test_results)} 项测试通过")
    
    if passed_tests == len(test_results):
        print("🎉 恭喜！所有功能测试通过，exe文件打包完整！")
        return True
    else:
        print("⚠️  部分功能测试失败，请检查打包配置")
        return False

if __name__ == "__main__":
    success = main()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 测试完成 - exe文件可以正常使用")
    else:
        print("❌ 测试完成 - 发现问题，需要修复")
    
    input("\n按回车键退出...")
