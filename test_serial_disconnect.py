#!/usr/bin/env python3
"""
测试串口断开功能是否正常工作
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PyQt5.QtCore import QTimer

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from ui.modules.weight_submission_module import SerialWorker
    print("✅ 成功导入 SerialWorker")
except ImportError as e:
    print(f"❌ 导入 SerialWorker 失败: {e}")
    sys.exit(1)

class SerialTestWindow(QMainWindow):
    """串口测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.serial_worker = None
        self.init_ui()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("串口断开测试")
        self.setGeometry(100, 100, 400, 300)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        self.status_label = QLabel("状态: 未连接")
        layout.addWidget(self.status_label)
        
        self.connect_btn = QPushButton("连接串口")
        self.connect_btn.clicked.connect(self.toggle_connection)
        layout.addWidget(self.connect_btn)
        
        self.test_btn = QPushButton("测试断开连接")
        self.test_btn.clicked.connect(self.test_disconnect)
        layout.addWidget(self.test_btn)
        
        self.log_label = QLabel("日志:")
        layout.addWidget(self.log_label)
        
    def toggle_connection(self):
        """切换连接状态"""
        if not self.serial_worker or not self.serial_worker.running:
            self.connect_serial()
        else:
            self.disconnect_serial()
    
    def connect_serial(self):
        """连接串口"""
        try:
            self.log("开始连接串口...")
            self.serial_worker = SerialWorker("COM4", 9600, 1, False)
            self.serial_worker.connection_status.connect(self.on_connection_status)
            self.serial_worker.error_occurred.connect(self.on_error)
            self.serial_worker.start()
            
        except Exception as e:
            self.log(f"连接失败: {e}")
    
    def disconnect_serial(self):
        """断开串口"""
        if self.serial_worker:
            self.log("开始断开串口...")
            start_time = time.time()
            
            # 停止串口工作线程
            self.serial_worker.stop()
            
            # 等待线程结束，设置超时
            if self.serial_worker.wait(3000):  # 等待3秒
                elapsed = time.time() - start_time
                self.log(f"串口断开成功，耗时: {elapsed:.2f}秒")
            else:
                self.log("串口断开超时，强制终止线程")
                self.serial_worker.terminate()
                self.serial_worker.wait(1000)
                elapsed = time.time() - start_time
                self.log(f"强制终止完成，总耗时: {elapsed:.2f}秒")
            
            self.serial_worker = None
            self.status_label.setText("状态: 已断开")
            self.connect_btn.setText("连接串口")
    
    def test_disconnect(self):
        """测试断开连接功能"""
        if not self.serial_worker or not self.serial_worker.running:
            self.log("请先连接串口")
            return
        
        self.log("开始测试断开连接...")
        
        # 模拟用户点击断开按钮
        QTimer.singleShot(100, self.disconnect_serial)
    
    def on_connection_status(self, connected):
        """连接状态变化"""
        if connected:
            self.status_label.setText("状态: 已连接")
            self.connect_btn.setText("断开串口")
            self.log("串口连接成功")
        else:
            self.status_label.setText("状态: 未连接")
            self.connect_btn.setText("连接串口")
            self.log("串口连接断开")
    
    def on_error(self, error):
        """错误处理"""
        self.log(f"错误: {error}")
    
    def log(self, message):
        """记录日志"""
        current_text = self.log_label.text()
        if current_text == "日志:":
            new_text = f"日志:\n{message}"
        else:
            new_text = f"{current_text}\n{message}"
        self.log_label.setText(new_text)
        print(message)
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.serial_worker:
            self.disconnect_serial()
        super().closeEvent(event)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    window = SerialTestWindow()
    window.show()
    
    print("🚀 串口断开测试程序启动")
    print("说明:")
    print("1. 点击'连接串口'按钮连接串口")
    print("2. 点击'测试断开连接'按钮测试断开功能")
    print("3. 观察是否出现卡死现象")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
